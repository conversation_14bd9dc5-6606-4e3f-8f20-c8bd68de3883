#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装chardet库用于文件编码检测
"""

import subprocess
import sys

def install_chardet():
    """安装chardet库"""
    try:
        print("正在安装chardet库...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'chardet'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ chardet库安装成功！")
            return True
        else:
            print(f"❌ chardet库安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")
        return False

def test_chardet():
    """测试chardet库"""
    try:
        import chardet
        print("✅ chardet库可以正常导入")
        
        # 测试编码检测
        test_text = "这是一个测试文本".encode('gbk')
        result = chardet.detect(test_text)
        print(f"✅ 编码检测测试成功: {result}")
        
        return True
        
    except ImportError:
        print("❌ chardet库导入失败")
        return False
    except Exception as e:
        print(f"❌ chardet库测试失败: {e}")
        return False

def main():
    """主函数"""
    print("chardet库安装工具")
    print("=" * 30)
    
    # 先检查是否已安装
    if test_chardet():
        print("chardet库已经安装并可正常使用")
        return
    
    print("chardet库未安装，开始安装...")
    
    if install_chardet():
        print("\n重新测试chardet库...")
        if test_chardet():
            print("\n🎉 chardet库安装并测试成功！")
            print("现在程序可以自动检测文件编码了")
        else:
            print("\n❌ chardet库安装成功但测试失败")
    else:
        print("\n❌ chardet库安装失败")
        print("你可以手动安装：pip install chardet")

if __name__ == "__main__":
    main()
