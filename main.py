from openai import OpenAI
import os
from docx import Document
import string
import time
import tiktoken
import re
import traceback
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import json


class APIConfigGUI:
    """API配置图形界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("智能伪原创工具 v2.3")
        self.root.geometry("900x650")
        self.root.resizable(True, True)

        # 设置窗口居中
        self.center_window()

        # API密钥变量
        self.api_key = tk.StringVar()
        self.api_key_saved = False
        self._switching_provider = False  # 标志是否正在切换提供方

        # 文件/文件夹路径变量
        self.input_path = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.input_mode = tk.StringVar(value="folder")  # "file" 或 "folder"

        # API提供方与模型选择
        self.provider = tk.StringVar(value="deepseek")  # deepseek | openai
        self.selected_model = tk.StringVar(value="deepseek-chat")
        self._previous_provider = "deepseek"  # 跟踪之前的提供方

        # 状态变量
        self.total_tokens = 0
        self.total_cost = 0.0
        self.current_file = ""
        self.current_step = ""
        self.processed_files = 0
        self.total_files = 0
        # 最近一次调用的显示值（避免被后续无token的更新覆盖为0）
        self.last_input_tokens = 0
        self.last_output_tokens = 0
        self.last_cost = 0.0

        # 创建界面
        self.create_widgets()

        # 加载已保存的配置
        self.load_config()

        # 绑定自动保存事件（trace_add 兼容新版本Tk）
        for var in (self.api_key, self.input_path, self.output_folder, self.selected_model):
            try:
                var.trace_add('write', self.auto_save_config)
            except Exception:
                var.trace('w', self.auto_save_config)

        # 单独处理提供方变化
        try:
            self.provider.trace_add('write', self._on_provider_var_change)
        except Exception:
            self.provider.trace('w', self._on_provider_var_change)

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """创建界面组件"""
        # 主框架 - 使用PanedWindow分割左右区域
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧配置区域
        left_frame = ttk.Frame(main_paned, padding="10")
        main_paned.add(left_frame, weight=2)

        # 右侧状态区域
        right_frame = ttk.Frame(main_paned, padding="10")
        main_paned.add(right_frame, weight=1)

        # === 左侧区域 ===
        # 标题和高级设置
        title_frame = ttk.Frame(left_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        title_frame.columnconfigure(0, weight=1)

        title_label = ttk.Label(title_frame, text="智能伪原创工具", font=("微软雅黑", 18, "bold"))
        title_label.grid(row=0, column=0, sticky=tk.W)

        self.settings_btn = ttk.Button(title_frame, text="高级设置", command=self.open_settings, width=12)
        self.settings_btn.grid(row=0, column=3, sticky=tk.E)

        # API配置区域
        api_frame = ttk.LabelFrame(left_frame, text="API配置", padding="15")
        api_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # API提供方选择
        provider_frame = ttk.Frame(api_frame)
        provider_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 8))

        ttk.Label(provider_frame, text="API提供方:", font=("微软雅黑", 10)).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Radiobutton(provider_frame, text="DeepSeek", variable=self.provider, value="deepseek").grid(row=0, column=1, padx=(0, 10))
        ttk.Radiobutton(provider_frame, text="OpenAI", variable=self.provider, value="openai").grid(row=0, column=2)

        # 模型选择
        model_frame = ttk.Frame(api_frame)
        model_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        model_frame.columnconfigure(1, weight=1)

        ttk.Label(model_frame, text="模型:", font=("微软雅黑", 10)).grid(row=0, column=0, sticky=tk.W)
        self.model_combo = ttk.Combobox(model_frame, textvariable=self.selected_model, values=["deepseek-chat"], state="readonly", width=30)
        self.model_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(8, 10))
        self.refresh_btn = ttk.Button(model_frame, text="刷新模型", command=self.refresh_models, width=10)
        self.refresh_btn.grid(row=0, column=2)

        # API密钥输入
        self.api_label = ttk.Label(api_frame, text="DeepSeek API密钥:", font=("微软雅黑", 10))
        self.api_label.grid(row=2, column=0, sticky=tk.W, pady=(0, 8))

        key_frame = ttk.Frame(api_frame)
        key_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        key_frame.columnconfigure(0, weight=1)

        self.key_entry = ttk.Entry(key_frame, textvariable=self.api_key, show="*", width=40, font=("Consolas", 10))
        self.key_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8))

        self.show_key_btn = ttk.Button(key_frame, text="显示", command=self.toggle_key_visibility, width=8)
        self.show_key_btn.grid(row=0, column=1, padx=(0, 8))

        self.test_btn = ttk.Button(key_frame, text="测试连接", command=self.test_connection, width=10)
        self.test_btn.grid(row=0, column=2)

        # 简化说明（根据提供方动态切换）
        self.info_label = ttk.Label(api_frame, text="获取密钥：访问 https://platform.deepseek.com/ 注册并创建API密钥",
                              foreground="gray", font=("微软雅黑", 9))
        self.info_label.grid(row=2, column=0, sticky=tk.W, pady=(0, 8))

        # 文件/文件夹配置区域
        path_frame = ttk.LabelFrame(left_frame, text="输入输出配置", padding="15")
        path_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # 输入模式选择
        mode_frame = ttk.Frame(path_frame)
        mode_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(mode_frame, text="输入模式:", font=("微软雅黑", 10)).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        ttk.Radiobutton(mode_frame, text="选择文件夹", variable=self.input_mode, value="folder",
                       command=self.on_mode_change).grid(row=0, column=1, padx=(0, 10))
        ttk.Radiobutton(mode_frame, text="选择单个文件", variable=self.input_mode, value="file",
                       command=self.on_mode_change).grid(row=0, column=2)

        # 输入路径
        self.input_label = ttk.Label(path_frame, text="输入文件夹:", font=("微软雅黑", 10))
        self.input_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 8))

        input_frame = ttk.Frame(path_frame)
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        input_frame.columnconfigure(0, weight=1)

        self.input_entry = ttk.Entry(input_frame, textvariable=self.input_path, width=40, font=("微软雅黑", 9))
        self.input_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8))

        self.select_input_btn = ttk.Button(input_frame, text="选择文件夹", command=self.select_input_path, width=12)
        self.select_input_btn.grid(row=0, column=1)

        # 输出文件夹
        ttk.Label(path_frame, text="输出文件夹:", font=("微软雅黑", 10)).grid(row=3, column=0, sticky=tk.W, pady=(0, 8))

        output_frame = ttk.Frame(path_frame)
        output_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        output_frame.columnconfigure(0, weight=1)

        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_folder, width=40, font=("微软雅黑", 9))
        self.output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8))

        ttk.Button(output_frame, text="选择文件夹", command=self.select_output_folder, width=12).grid(row=0, column=1)

        # 输出格式选择
        format_frame = ttk.Frame(path_frame)
        format_frame.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(format_frame, text="输出格式:", font=("微软雅黑", 10)).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.output_format = tk.StringVar(value="auto")
        ttk.Radiobutton(format_frame, text="自动（保持原格式）", variable=self.output_format, value="auto").grid(row=0, column=1, padx=(0, 10))
        ttk.Radiobutton(format_frame, text="文本文件(.txt)", variable=self.output_format, value="txt").grid(row=0, column=2, padx=(0, 10))
        ttk.Radiobutton(format_frame, text="Word文档(.docx)", variable=self.output_format, value="docx").grid(row=0, column=3)

        # 开始处理按钮
        self.start_btn = ttk.Button(left_frame, text="开始处理", command=self.start_processing,
                                   state="disabled", width=20, style="Accent.TButton")
        self.start_btn.grid(row=3, column=0, pady=(15, 0))

        # 状态显示
        self.status_var = tk.StringVar(value="请输入API密钥并选择文件路径")
        status_label = ttk.Label(left_frame, textvariable=self.status_var, foreground="blue", font=("微软雅黑", 10))
        status_label.grid(row=4, column=0, pady=(10, 0))

        # === 右侧状态区域 ===
        self.create_status_panel(right_frame)

        # 配置网格权重
        left_frame.columnconfigure(0, weight=1)
        api_frame.columnconfigure(0, weight=1)
        path_frame.columnconfigure(0, weight=1)

    def create_status_panel(self, parent):
        """创建状态面板"""
        # 状态面板标题
        status_title = ttk.Label(parent, text="处理状态", font=("微软雅黑", 14, "bold"))
        status_title.grid(row=0, column=0, sticky=tk.W, pady=(0, 15))

        # Token使用情况
        token_frame = ttk.LabelFrame(parent, text="Token使用情况", padding="10")
        token_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        self.token_info = tk.StringVar(value="总Token: 0\n输入Token: 0\n输出Token: 0")
        token_label = ttk.Label(token_frame, textvariable=self.token_info, font=("Consolas", 10))
        token_label.grid(row=0, column=0, sticky=tk.W)

        # 费用情况
        cost_frame = ttk.LabelFrame(parent, text="费用情况", padding="10")
        cost_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        self.cost_info = tk.StringVar(value="总费用: ¥0.00\n当前时段: 标准\n预计费用: ¥0.00")
        cost_label = ttk.Label(cost_frame, textvariable=self.cost_info, font=("Consolas", 10))
        cost_label.grid(row=0, column=0, sticky=tk.W)

        # 处理进度
        progress_frame = ttk.LabelFrame(parent, text="处理进度", padding="10")
        progress_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        self.progress_info = tk.StringVar(value="当前文件: 无\n处理步骤: 等待开始\n进度: 0/0")
        progress_label = ttk.Label(progress_frame, textvariable=self.progress_info, font=("微软雅黑", 10))
        progress_label.grid(row=0, column=0, sticky=tk.W)

        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # 实时日志
        log_frame = ttk.LabelFrame(parent, text="处理日志", padding="10")
        log_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 创建滚动文本框
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_text_frame.columnconfigure(0, weight=1)
        log_text_frame.rowconfigure(0, weight=1)

        self.log_text = tk.Text(log_text_frame, height=8, width=30, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置权重
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(4, weight=1)
        token_frame.columnconfigure(0, weight=1)
        cost_frame.columnconfigure(0, weight=1)
        progress_frame.columnconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def on_mode_change(self):
        """输入模式改变时的处理"""
        if self.input_mode.get() == "file":
            self.input_label.config(text="输入文件:")
            self.select_input_btn.config(text="选择文件")
        else:
            self.input_label.config(text="输入文件夹:")
            self.select_input_btn.config(text="选择文件夹")

        # 清空当前路径
        self.input_path.set("")

    def select_input_path(self):
        """选择输入文件或文件夹"""
        if self.input_mode.get() == "file":
            file_path = filedialog.askopenfilename(
                title="选择要处理的文件",
                filetypes=[("支持的文件", "*.txt *.docx"), ("文本文件", "*.txt"), ("Word文档", "*.docx"), ("所有文件", "*.*")]
            )
            if file_path:
                self.input_path.set(file_path)
        else:
            folder = filedialog.askdirectory(title="选择输入文件夹")
            if folder:
                self.input_path.set(folder)

    def toggle_key_visibility(self):
        """切换密钥显示/隐藏"""
        if self.key_entry.cget('show') == '*':
            self.key_entry.config(show='')
            self.show_key_btn.config(text='隐藏')
        else:
            self.key_entry.config(show='*')
            self.show_key_btn.config(text='显示')

    def auto_save_config(self, *args):
        """自动保存配置 - 只更新必要字段，不覆盖其他配置"""
        try:
            # 如果正在切换提供方，跳过自动保存
            if hasattr(self, '_switching_provider') and self._switching_provider:
                return


            api_key = self.api_key.get().strip()
            input_path = self.input_path.get().strip()
            output_folder = self.output_folder.get().strip()

            # 只有在有内容时才保存
            if api_key or input_path or output_folder:
                # 读取现有配置
                config = {}
                if os.path.exists('config.json'):
                    try:
                        with open('config.json', 'r', encoding='utf-8') as f:
                            config = json.load(f)
                    except:
                        pass

                # 只更新基本配置，保留其他设置（移除重复的api_key字段）
                config.update({
                    'input_path': input_path,
                    'input_mode': self.input_mode.get(),
                    'output_folder': output_folder,
                    'output_format': self.output_format.get(),
                    'provider': self.provider.get(),
                    'selected_model': self.selected_model.get()
                })

                # 移除顶层的api_key字段（如果存在）
                if 'api_key' in config:
                    del config['api_key']

                # 确保api_configs存在并分别管理deepseek/openai
                if 'api_configs' not in config:
                    config['api_configs'] = {}
                if 'deepseek' not in config['api_configs']:
                    config['api_configs']['deepseek'] = {
                        'base_url': "https://api.deepseek.com/v1",
                        'model': "deepseek-chat",
                        'api_key': ''
                    }
                if 'openai' not in config['api_configs']:
                    config['api_configs']['openai'] = {
                        'base_url': "https://api.openai.com/v1",
                        'model': "gpt-4o-mini",
                        'api_key': ''
                    }

                # 将当前输入的密钥保存到当前提供方
                current = self.provider.get()
                config['api_configs'][current]['api_key'] = api_key
                # 保存当前选择的模型到对应提供方
                config['api_configs'][current]['model'] = self.selected_model.get()

                # 确保其他配置存在默认值（如果不存在的话）
                if 'rewrite_strategies' not in config:
                    config['rewrite_strategies'] = {
                        'synonym_replacement': True,
                        'sentence_restructure': True,
                        'paragraph_reorder': True,
                        'style_variation': True,
                        'back_translation': False
                    }

                if 'rewrite_ratio' not in config:
                    config['rewrite_ratio'] = {
                        'min': 70,
                        'max': 90
                    }

                if 'processing_config' not in config:
                    config['processing_config'] = {
                        'max_text_length': 12000,
                        'max_retries': 3,
                        'enable_multi_round': True,
                        'enable_quality_check': True
                    }

                with open('config.json', 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

                # 检查是否可以启用开始按钮
                if api_key and (input_path or output_folder):
                    self.start_btn.config(state="normal")
                    if api_key and input_path and output_folder:
                        self.status_var.set("✅ 配置完成，可以开始处理")
                    else:
                        self.status_var.set("⚠️ 请完善配置信息")
                else:
                    self.start_btn.config(state="disabled")

        except Exception as e:
            print(f"自动保存配置失败: {e}")

    def update_status_display(self, input_tokens=0, output_tokens=0, cost=0.0, current_file="", step="", progress=0, total=0):
        """更新状态显示"""
        try:
            # 更新最近一次的明细值，用于显示“本次”数据
            if input_tokens or output_tokens:
                self.last_input_tokens = input_tokens
                self.last_output_tokens = output_tokens
            if cost:
                self.last_cost = cost

            # 累加总数
            if input_tokens > 0 or output_tokens > 0:
                self.total_tokens += input_tokens + output_tokens
            if cost > 0:
                self.total_cost += cost

            # Token显示：使用最近一次的输入/输出明细
            token_text = (
                f"总Token: {self.total_tokens:,}\n"
                f"输入Token: {self.last_input_tokens:,}\n"
                f"输出Token: {self.last_output_tokens:,}"
            )
            self.token_info.set(token_text)

            # 费用显示：使用最近一次的费用明细
            import datetime
            current_hour = datetime.datetime.now().hour
            time_period = "优惠时段" if 0 <= current_hour < 8 or current_hour >= 24 else "标准时段"
            cost_text = (
                f"总费用: ¥{self.total_cost:.4f}\n"
                f"当前时段: {time_period}\n"
                f"本次费用: ¥{self.last_cost:.4f}"
            )
            self.cost_info.set(cost_text)

            # 处理进度
            if current_file:
                self.current_file = current_file
            if step:
                self.current_step = step
            if total > 0:
                self.total_files = total
            if progress >= 0:
                self.processed_files = progress
            progress_text = f"当前文件: {self.current_file}\n处理步骤: {self.current_step}\n进度: {self.processed_files}/{self.total_files}"
            self.progress_info.set(progress_text)

            # 进度条
            if self.total_files > 0:
                progress_percent = (self.processed_files / self.total_files) * 100
                self.progress_bar['value'] = progress_percent

        except Exception as e:
            print(f"GUI状态更新失败: {e}")
            import traceback
            traceback.print_exc()

        # 更新进度信息
        if current_file:
            self.current_file = current_file
        if step:
            self.current_step = step
        if total > 0:
            self.total_files = total
        if progress >= 0:
            self.processed_files = progress

        progress_text = f"当前文件: {self.current_file}\n处理步骤: {self.current_step}\n进度: {self.processed_files}/{self.total_files}"
        self.progress_info.set(progress_text)

        # 更新进度条
        if self.total_files > 0:
            progress_percent = (self.processed_files / self.total_files) * 100
            self.progress_bar['value'] = progress_percent

    def add_log(self, message):
        """添加日志信息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def select_input_folder(self):
        """选择输入文件夹"""
        folder = filedialog.askdirectory(title="选择输入文件夹")
        if folder:
            self.input_folder.set(folder)

    def select_output_folder(self):
        """选择输出文件夹"""
        folder = filedialog.askdirectory(title="选择输出文件夹")
        if folder:
            self.output_folder.set(folder)

    def load_config(self):
        """加载已保存的配置"""
        try:
            # 优先加载新的config.json
            config_file = 'config.json'
            if not os.path.exists(config_file):
                # 兼容旧的api_config.json
                config_file = 'api_config.json'

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                    # 读取提供方和模型选择
                    provider = config.get('provider', 'deepseek')
                    self.provider.set(provider)
                    self._previous_provider = provider  # 初始化之前的提供方

                    selected_model = config.get('selected_model')
                    if not selected_model and 'api_configs' in config and provider in config['api_configs']:
                        selected_model = config['api_configs'][provider].get('model')
                    if not selected_model:
                        selected_model = 'gpt-4o-mini' if provider == 'openai' else 'deepseek-chat'
                    self.selected_model.set(selected_model)

                    # 获取API密钥（按提供方读取，兼容旧格式primary）
                    api_key = ''
                    if 'api_configs' in config and provider in config['api_configs']:
                        api_key = config['api_configs'][provider].get('api_key', '')
                    elif 'api_configs' in config and 'primary' in config['api_configs']:
                        api_key = config['api_configs']['primary'].get('api_key', '')
                    elif 'api_key' in config:
                        api_key = config['api_key']

                    if api_key:
                        self.api_key.set(api_key)
                        self.api_key_saved = True

                    # 更新API密钥标签
                    self.api_label.config(text=("OpenAI API密钥:" if provider == 'openai' else "DeepSeek API密钥:"))

                    # 初始化模型下拉
                    if provider == 'openai':
                        self.model_combo['values'] = [selected_model]  # 先占位，用户可点击“刷新模型”
                    else:
                        self.model_combo['values'] = ["deepseek-chat", "deepseek-reasoner"]
                    self.model_combo.set(selected_model)

                    # 加载路径配置
                    if 'input_path' in config:
                        self.input_path.set(config['input_path'])
                    elif 'input_folder' in config:  # 兼容旧版本
                        self.input_path.set(config['input_folder'])

                    if 'input_mode' in config:
                        self.input_mode.set(config['input_mode'])
                        self.on_mode_change()  # 更新界面

                    if 'output_folder' in config:
                        self.output_folder.set(config['output_folder'])

                    if 'output_format' in config:
                        self.output_format.set(config['output_format'])

                    # 检查配置完整性
                    input_path = self.input_path.get().strip()
                    output_folder = self.output_folder.get().strip()

                    if api_key and input_path and output_folder:
                        self.start_btn.config(state="normal")
                        self.status_var.set("✅ 配置已加载，可以开始处理")
                        self.add_log("配置加载完成")
                    elif api_key:
                        self.status_var.set("⚠️ 请选择输入输出路径")
                    else:
                        self.status_var.set("请输入API密钥")

        except Exception as e:
            print(f"加载配置失败: {e}")
            self.add_log(f"配置加载失败: {e}")

    def open_settings(self):
        """打开高级设置界面"""
        SettingsWindow(self.root)

    def test_connection(self):
        """测试API连接"""
        api_key = self.api_key.get().strip()
        if not api_key:
            messagebox.showerror("错误", "请输入API密钥")
            return

        self.status_var.set("正在测试连接...")
        self.test_btn.config(state="disabled")

        # 在新线程中测试连接
        threading.Thread(target=self._test_connection_thread, args=(api_key,), daemon=True).start()

    def _on_provider_var_change(self, *args):
        """当提供方变量改变时调用"""
        # 延迟执行，确保变量已经更新
        self.root.after(1, self.on_provider_change)

    def _handle_provider_change(self):
        """处理提供方切换的包装方法"""
        # 延迟执行，确保在自动保存触发后再处理
        self.root.after(10, self.on_provider_change)

    def on_provider_change(self):
        """切换API提供方时更新UI和配置"""
        # 立即禁用自动保存，避免在切换过程中触发
        self._switching_provider = True

        new_provider = self.provider.get()
        current_provider = self._previous_provider  # 切换前的提供方

        # 先保存当前API密钥到切换前的提供方
        self._save_api_key_to_provider(current_provider)

        # 更新标签
        self.api_label.config(text=("OpenAI API密钥:" if new_provider == 'openai' else "DeepSeek API密钥:"))
        self.info_label.config(text=(
            "获取密钥：访问 https://platform.openai.com/ 注册并创建API密钥" if new_provider == 'openai'
            else "获取密钥：访问 https://platform.deepseek.com/ 注册并创建API密钥"
        ))

        # 加载新提供方的API密钥
        self._load_provider_api_key(new_provider)

        # 更新模型列表和选择
        if new_provider == 'openai':
            # 加载保存的OpenAI模型，如果没有则使用默认值
            saved_model = self._get_saved_model(new_provider) or 'gpt-4o-mini'
            self.selected_model.set(saved_model)
            self.model_combo['values'] = [saved_model]  # 先占位，用户可点击"刷新模型"
        else:
            # DeepSeek固定模型列表
            saved_model = self._get_saved_model(new_provider) or 'deepseek-chat'
            self.model_combo['values'] = ["deepseek-chat", "deepseek-reasoner"]
            if saved_model not in self.model_combo['values']:
                saved_model = "deepseek-chat"
            self.selected_model.set(saved_model)

        # 更新之前的提供方跟踪
        self._previous_provider = new_provider

        # 重新启用自动保存
        self._switching_provider = False

        # 不需要手动触发保存，因为我们已经在 _save_api_key_to_provider 中保存了
        # 避免重复保存导致覆盖问题

        # 添加切换日志
        self.add_log(f"已切换到 {new_provider.upper()} 提供方")

    def _save_api_key_to_provider(self, provider):
        """保存当前API密钥到指定提供方的配置中"""
        try:
            current_api_key = self.api_key.get().strip()



            if not current_api_key:
                return

            # 读取现有配置
            config = {}
            if os.path.exists('config.json'):
                try:
                    with open('config.json', 'r', encoding='utf-8') as f:
                        config = json.load(f)
                except:
                    pass

            # 确保api_configs结构存在
            if 'api_configs' not in config:
                config['api_configs'] = {}
            if provider not in config['api_configs']:
                config['api_configs'][provider] = {}

            # 保存API密钥和当前模型
            config['api_configs'][provider]['api_key'] = current_api_key
            config['api_configs'][provider]['model'] = self.selected_model.get()

            # 设置默认的base_url
            if 'base_url' not in config['api_configs'][provider]:
                if provider == 'openai':
                    config['api_configs'][provider]['base_url'] = "https://api.openai.com/v1"
                else:
                    config['api_configs'][provider]['base_url'] = "https://api.deepseek.com/v1"

            # 保存配置
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)



        except Exception as e:
            print(f"保存API密钥失败: {e}")

    def _save_current_api_key(self):
        """保存当前API密钥到当前提供方的配置中（兼容旧方法）"""
        current_provider = self.provider.get()
        self._save_api_key_to_provider(current_provider)

    def _load_provider_api_key(self, provider):
        """加载指定提供方的API密钥"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 获取指定提供方的API密钥
                api_key = ''
                if 'api_configs' in config and provider in config['api_configs']:
                    api_key = config['api_configs'][provider].get('api_key', '')

                # 更新API密钥输入框
                self.api_key.set(api_key)
                self.api_key_saved = bool(api_key)

                # 添加日志
                if api_key:
                    self.add_log(f"已加载 {provider.upper()} API密钥")
                else:
                    self.add_log(f"未找到 {provider.upper()} API密钥，请输入")

        except Exception as e:
            print(f"加载API密钥失败: {e}")
            self.api_key.set('')
            self.api_key_saved = False
            self.add_log(f"加载 {provider.upper()} API密钥失败: {e}")

    def _get_saved_model(self, provider):
        """获取指定提供方保存的模型"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)

                if 'api_configs' in config and provider in config['api_configs']:
                    return config['api_configs'][provider].get('model', '')
        except Exception as e:
            print(f"获取保存的模型失败: {e}")
        return ''

    def refresh_models(self):
        """刷新模型列表（放到后台线程，避免卡住UI）"""
        def worker():
            try:
                provider = self.provider.get()
                api_key = self.api_key.get().strip()

                # 添加开始刷新的日志
                self.root.after(0, lambda: self.add_log(f"开始刷新{provider.upper()}模型列表..."))

                if not api_key:
                    self.root.after(0, lambda: messagebox.showerror("错误", "请先填写API密钥"))
                    self.root.after(0, lambda: self.add_log("❌ 刷新失败：未填写API密钥"))
                    return

                from openai import OpenAI
                import httpx

                if provider == 'openai':
                    # 先直连拉取，如果失败再走本地代理
                    def build_client(use_proxy=False):
                        if use_proxy:
                            try:
                                http_client = httpx.Client(timeout=30.0, proxies="http://127.0.0.1:7890")
                            except TypeError:
                                import os
                                os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
                                os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'
                                http_client = httpx.Client(timeout=10.0, trust_env=True)
                        else:
                            http_client = httpx.Client(timeout=5.0, trust_env=False)
                        return OpenAI(base_url="https://api.openai.com/v1", api_key=api_key, http_client=http_client)

                    models = None
                    try:
                        # 添加日志：尝试直连
                        self.root.after(0, lambda: self.add_log("正在尝试直连OpenAI API..."))
                        client = build_client(False)
                        models = client.models.list()
                        # 添加日志：直连成功
                        self.root.after(0, lambda: self.add_log("✅ 直连OpenAI API成功"))
                    except Exception as direct_error:
                        # 添加日志：直连失败，尝试代理
                        error_msg = str(direct_error)
                        self.root.after(0, lambda: self.add_log(f"❌ 直连失败: {error_msg[:50]}..."))
                        self.root.after(0, lambda: self.add_log("正在尝试通过代理连接..."))
                        try:
                            client = build_client(True)
                            models = client.models.list()
                            # 添加日志：代理连接成功
                            self.root.after(0, lambda: self.add_log("✅ 代理连接成功"))
                        except Exception as proxy_error:
                            # 添加日志：代理也失败
                            proxy_error_msg = str(proxy_error)
                            self.root.after(0, lambda: self.add_log(f"❌ 代理连接也失败: {proxy_error_msg[:50]}..."))
                            raise proxy_error

                    names = []
                    if hasattr(models, 'data'):
                        for m in models.data:
                            name = getattr(m, 'id', '')
                            if name and any(kw in name for kw in ['gpt', 'o', 'mini']):
                                names.append(name)
                    if not names:
                        names = [self.selected_model.get() or 'gpt-4o-mini']

                    def apply_openai():
                        self.model_combo['values'] = names
                        current = self.selected_model.get()
                        if current not in names:
                            self.selected_model.set(names[0])
                        messagebox.showinfo("提示", f"已加载 {len(names)} 个OpenAI模型")
                    self.root.after(0, apply_openai)
                    self.root.after(0, lambda: self.add_log(f"✅ 成功加载 {len(names)} 个OpenAI模型"))
                else:
                    # DeepSeek 固定列表
                    self.root.after(0, lambda: self.add_log("正在加载DeepSeek模型列表..."))
                    names = ["deepseek-chat", "deepseek-reasoner"]
                    def apply_ds():
                        self.model_combo['values'] = names
                        if self.selected_model.get() not in names:
                            self.selected_model.set("deepseek-chat")
                        messagebox.showinfo("提示", "已加载DeepSeek模型列表")
                    self.root.after(0, apply_ds)
                    self.root.after(0, lambda: self.add_log("✅ DeepSeek模型列表加载完成"))
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: messagebox.showerror("错误", f"刷新模型失败：{error_msg}"))
            finally:
                self.root.after(0, lambda: self.refresh_btn.config(state="normal"))

        # 禁用按钮，启动后台线程
        self.refresh_btn.config(state="disabled")
        threading.Thread(target=worker, daemon=True).start()

    def _test_connection_thread(self, api_key):
        """在后台线程中测试连接；失败时自动回退到本地代理；支持DeepSeek/OpenAI"""
        try:
            from openai import OpenAI
            import httpx

            provider = self.provider.get()
            # 基础配置
            if provider == 'openai':
                base_url = "https://api.openai.com/v1"
                model = self.selected_model.get() or "gpt-4o-mini"
            else:
                base_url = "https://api.deepseek.com/v1"
                model = self.selected_model.get() or "deepseek-chat"

            def build_client(use_proxy=False):
                if use_proxy:
                    try:
                        http_client = httpx.Client(timeout=30.0, proxies="http://127.0.0.1:7890")
                    except TypeError:
                        import os
                        os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
                        os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'
                        http_client = httpx.Client(timeout=30.0, trust_env=True)
                else:
                    http_client = httpx.Client(timeout=30.0, trust_env=False)
                return OpenAI(base_url=base_url, api_key=api_key, http_client=http_client)

            # 先直连
            try:
                client = build_client(use_proxy=False)
                client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": "测试连接"}],
                    max_tokens=5,
                    temperature=0.1
                )
                self.root.after(0, self._connection_success)
                return
            except Exception as e1:
                # 回退到本地代理
                try:
                    client = build_client(use_proxy=True)
                    client.chat.completions.create(
                        model=model,
                        messages=[{"role": "user", "content": "测试连接(代理)"}],
                        max_tokens=5,
                        temperature=0.1
                    )
                    self.root.after(0, self._connection_success)
                    return
                except Exception as e2:
                    self.root.after(0, self._connection_failed, f"直连失败: {e1}\n代理失败: {e2}")
        except Exception as e:
            self.root.after(0, self._connection_failed, str(e))

    def _connection_success(self):
        """连接成功的回调"""
        self.status_var.set("✅ API连接测试成功！")
        self.test_btn.config(state="normal")
        messagebox.showinfo("成功", "API连接测试成功！")

    def _connection_failed(self, error_msg):
        """连接失败的回调"""
        self.status_var.set("❌ API连接测试失败")
        self.test_btn.config(state="normal")
        messagebox.showerror("连接失败", f"API连接测试失败：\n{error_msg}")

    def start_processing(self):
        """开始处理文件"""
        api_key = self.api_key.get().strip()
        input_path = self.input_path.get().strip()
        output_folder = self.output_folder.get().strip()

        if not api_key:
            messagebox.showerror("错误", "请输入API密钥")
            return

        if not input_path:
            messagebox.showerror("错误", "请选择输入文件或文件夹")
            return

        if not output_folder:
            messagebox.showerror("错误", "请选择输出文件夹")
            return

        # 验证路径存在
        if not os.path.exists(input_path):
            messagebox.showerror("错误", f"输入路径不存在：{input_path}")
            return

        if not os.path.exists(output_folder):
            try:
                os.makedirs(output_folder)
            except Exception as e:
                messagebox.showerror("错误", f"无法创建输出文件夹：{e}")
                return

        # 重置状态
        self.total_tokens = 0
        self.total_cost = 0.0
        self.processed_files = 0
        self.total_files = 0
        self.log_text.delete(1.0, tk.END)

        self.add_log("开始处理...")

        # 启动文本改写器
        try:
            rewriter = TextRewriter()
            rewriter.gui = self  # 传递GUI引用以便更新状态

            # 在后台线程中运行处理，避免阻塞GUI
            import threading
            self.processing_thread = threading.Thread(target=self._run_processing, args=(rewriter,), daemon=True)
            self.processing_thread.start()

            # 禁用开始按钮，防止重复启动
            self.start_btn.config(state="disabled", text="处理中...")

        except Exception as e:
            messagebox.showerror("错误", f"启动处理失败：{e}")
            self.add_log(f"启动失败：{e}")

    def _run_processing(self, rewriter):
        """在后台线程中运行处理"""
        try:
            # 在后台线程中运行处理
            rewriter.run()

            # 处理完成后，在主线程中更新GUI
            self.root.after(0, self._processing_completed)

        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, lambda: self._processing_error(e))

    def _processing_completed(self):
        """处理完成后的GUI更新"""
        self.start_btn.config(state="normal", text="开始处理")
        self.add_log("🎉 所有文件处理完成！")
        messagebox.showinfo("完成", "文件处理完成！")

    def _processing_error(self, error):
        """处理错误时的GUI更新"""
        self.start_btn.config(state="normal", text="开始处理")
        self.add_log(f"处理错误：{error}")
        messagebox.showerror("处理错误", f"处理过程中出现错误：{error}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()


class SettingsWindow:
    """高级设置窗口"""

    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("高级设置")
        self.window.geometry("350x600")
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.grab_set()

        # 居中显示
        self.center_window()

        # 加载当前配置
        self.load_current_config()

        # 创建界面
        self.create_widgets()

    def center_window(self):
        """窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def load_current_config(self):
        """加载当前配置"""
        try:
            # 从JSON文件加载配置
            config_file = 'config.json'
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                self.rewrite_ratio = config.get('rewrite_ratio', {'min': 70, 'max': 90})
                self.processing_config = config.get('processing_config', {
                    'max_text_length': 12000,
                    'max_retries': 3,
                    'enable_multi_round': True,
                    'enable_quality_check': True
                })
                # 确保所有策略字段都存在
                default_strategies = {
                    'synonym_replacement': True,
                    'sentence_restructure': True,
                    'paragraph_reorder': True,
                    'style_variation': True,
                    'back_translation': False
                }
                self.rewrite_strategies = config.get('rewrite_strategies', {})
                # 补充缺失的字段
                for key, default_value in default_strategies.items():
                    if key not in self.rewrite_strategies:
                        self.rewrite_strategies[key] = default_value
            else:
                # 默认配置
                self.rewrite_ratio = {'min': 70, 'max': 90}
                self.processing_config = {
                    'max_text_length': 12000,
                    'max_retries': 3,
                    'enable_multi_round': True,
                    'enable_quality_check': True
                }
                self.rewrite_strategies = {
                    'synonym_replacement': True,
                    'sentence_restructure': True,
                    'paragraph_reorder': True,
                    'style_variation': True,
                    'back_translation': False
                }
        except Exception as e:
            print(f"加载配置失败: {e}")
            # 使用默认配置
            self.rewrite_ratio = {'min': 70, 'max': 90}
            self.processing_config = {
                'max_text_length': 12000,
                'max_retries': 3,
                'enable_multi_round': True,
                'enable_quality_check': True
            }
            self.rewrite_strategies = {
                'synonym_replacement': True,
                'sentence_restructure': True,
                'paragraph_reorder': True,
                'style_variation': True,
                'back_translation': False
            }

    def create_widgets(self):
        """创建设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建滚动框架
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 改写质量设置
        quality_frame = ttk.LabelFrame(scrollable_frame, text="改写质量设置", padding="15")
        quality_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # 改写幅度
        ttk.Label(quality_frame, text="改写幅度范围:", font=("微软雅黑", 10)).grid(row=0, column=0, sticky=tk.W, pady=(0, 8))

        ratio_frame = ttk.Frame(quality_frame)
        ratio_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(ratio_frame, text="最小:").grid(row=0, column=0, padx=(0, 5))
        self.min_ratio = tk.IntVar(value=self.rewrite_ratio['min'])
        min_spin = ttk.Spinbox(ratio_frame, from_=50, to=100, textvariable=self.min_ratio, width=8)
        min_spin.grid(row=0, column=1, padx=(0, 15))

        ttk.Label(ratio_frame, text="最大:").grid(row=0, column=2, padx=(0, 5))
        self.max_ratio = tk.IntVar(value=self.rewrite_ratio['max'])
        max_spin = ttk.Spinbox(ratio_frame, from_=50, to=150, textvariable=self.max_ratio, width=8)
        max_spin.grid(row=0, column=3)

        ttk.Label(ratio_frame, text="%").grid(row=0, column=4, padx=(5, 0))

        # 处理设置
        process_frame = ttk.LabelFrame(scrollable_frame, text="处理设置", padding="15")
        process_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # 最大文本长度
        ttk.Label(process_frame, text="单次处理最大长度:", font=("微软雅黑", 10)).grid(row=0, column=0, sticky=tk.W, pady=(0, 8))
        self.max_length = tk.IntVar(value=self.processing_config['max_text_length'])
        length_spin = ttk.Spinbox(process_frame, from_=5000, to=20000, textvariable=self.max_length, width=10)
        length_spin.grid(row=0, column=1, sticky=tk.W, pady=(0, 8))
        ttk.Label(process_frame, text="字符").grid(row=0, column=2, padx=(5, 0), pady=(0, 8))

        # 最大重试次数
        ttk.Label(process_frame, text="最大重试次数:", font=("微软雅黑", 10)).grid(row=1, column=0, sticky=tk.W, pady=(0, 8))
        self.max_retries = tk.IntVar(value=self.processing_config['max_retries'])
        retry_spin = ttk.Spinbox(process_frame, from_=1, to=10, textvariable=self.max_retries, width=10)
        retry_spin.grid(row=1, column=1, sticky=tk.W, pady=(0, 8))
        ttk.Label(process_frame, text="次").grid(row=1, column=2, padx=(5, 0), pady=(0, 8))

        # 开关设置
        switch_frame = ttk.LabelFrame(scrollable_frame, text="功能开关", padding="15")
        switch_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        self.enable_multi_round = tk.BooleanVar(value=self.processing_config['enable_multi_round'])
        ttk.Checkbutton(switch_frame, text="启用多轮改写（效果更好，成本更高）",
                       variable=self.enable_multi_round).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        self.enable_quality_check = tk.BooleanVar(value=self.processing_config['enable_quality_check'])
        ttk.Checkbutton(switch_frame, text="启用质量检查",
                       variable=self.enable_quality_check).grid(row=1, column=0, sticky=tk.W, pady=(0, 5))

        # 改写策略
        strategy_frame = ttk.LabelFrame(scrollable_frame, text="改写策略", padding="15")
        strategy_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        self.synonym_replacement = tk.BooleanVar(value=self.rewrite_strategies['synonym_replacement'])
        ttk.Checkbutton(strategy_frame, text="同义词替换",
                       variable=self.synonym_replacement).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        self.sentence_restructure = tk.BooleanVar(value=self.rewrite_strategies['sentence_restructure'])
        ttk.Checkbutton(strategy_frame, text="句式重构",
                       variable=self.sentence_restructure).grid(row=1, column=0, sticky=tk.W, pady=(0, 5))

        self.paragraph_reorder = tk.BooleanVar(value=self.rewrite_strategies['paragraph_reorder'])
        ttk.Checkbutton(strategy_frame, text="段落重组",
                       variable=self.paragraph_reorder).grid(row=2, column=0, sticky=tk.W, pady=(0, 5))

        self.style_variation = tk.BooleanVar(value=self.rewrite_strategies['style_variation'])
        ttk.Checkbutton(strategy_frame, text="语言风格变化",
                       variable=self.style_variation).grid(row=3, column=0, sticky=tk.W, pady=(0, 5))

        self.back_translation = tk.BooleanVar(value=self.rewrite_strategies['back_translation'])
        ttk.Checkbutton(strategy_frame, text="翻译回译（成本较高）",
                       variable=self.back_translation).grid(row=4, column=0, sticky=tk.W, pady=(0, 5))

        # 按钮区域
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.grid(row=4, column=0, pady=(20, 0))

        ttk.Button(button_frame, text="保存设置", command=self.save_settings, width=12).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="恢复默认", command=self.reset_defaults, width=12).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=self.window.destroy, width=12).grid(row=0, column=2)

        # 配置滚动
        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=1)
        scrollable_frame.columnconfigure(0, weight=1)

    def save_settings(self):
        """保存高级设置 - 只更新高级设置，不影响基本配置"""
        try:
            # 读取现有配置
            config = {}
            if os.path.exists('config.json'):
                with open('config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 只更新高级设置部分，保留其他配置
            config['rewrite_strategies'] = {
                'synonym_replacement': self.synonym_replacement.get(),
                'sentence_restructure': self.sentence_restructure.get(),
                'paragraph_reorder': self.paragraph_reorder.get(),
                'style_variation': self.style_variation.get(),
                'back_translation': self.back_translation.get()
            }

            config['rewrite_ratio'] = {
                'min': self.min_ratio.get(),
                'max': self.max_ratio.get()
            }

            config['processing_config'] = {
                'max_text_length': self.max_length.get(),
                'max_retries': self.max_retries.get(),
                'enable_multi_round': self.enable_multi_round.get(),
                'enable_quality_check': self.enable_quality_check.get()
            }

            # 确保API配置存在但不覆盖现有的API密钥
            if 'api_configs' not in config:
                config['api_configs'] = {}
            if 'primary' not in config['api_configs']:
                config['api_configs']['primary'] = {}

            # 保留现有的API配置，只确保必要字段存在
            primary_config = config['api_configs']['primary']
            if 'base_url' not in primary_config:
                primary_config['base_url'] = "https://api.deepseek.com/v1"
            if 'model' not in primary_config:
                primary_config['model'] = "deepseek-chat"
            if 'api_key' not in primary_config:
                primary_config['api_key'] = config.get('api_key', '')

            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("成功", "高级设置已保存！")
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败：{str(e)}")

    def reset_defaults(self):
        """恢复默认设置"""
        self.min_ratio.set(70)
        self.max_ratio.set(90)
        self.max_length.set(12000)
        self.max_retries.set(3)
        self.enable_multi_round.set(True)
        self.enable_quality_check.set(True)
        self.synonym_replacement.set(True)
        self.sentence_restructure.set(True)
        self.paragraph_reorder.set(True)
        self.style_variation.set(True)
        self.back_translation.set(False)


class TextRewriter:
    def __init__(self):
        """初始化改写器"""
        # 不强制屏蔽系统代理，允许按需回退到本地代理（127.0.0.1:7890）

        # ====== 加载配置 ======
        self.api_configs = self._load_config()
        self.folder_config = self._load_folder_config()

        # 如果没有配置文件，使用默认配置
        if not self.api_configs:
            print("未找到有效配置，使用默认DeepSeek配置")
            self.api_configs = {
                'primary': {
                    'base_url': "https://api.deepseek.com/v1",
                    'api_key': "",  # 将通过GUI设置
                    'model': "deepseek-chat"
                }
            }

        # 初始化主要API客户端（根据配置的provider选择 deepseek/openai；兼容旧版primary）
        provider = (self.folder_config or {}).get('provider', 'deepseek')
        if provider not in self.api_configs and 'primary' in self.api_configs:
            # 兼容旧配置：将primary映射为当前provider
            self.api_configs[provider] = self.api_configs['primary']
        self.current_api = provider if provider in self.api_configs else (list(self.api_configs.keys())[0])
        self.client = self._init_client(self.current_api)

        # ====== 配置项 ======
        # 文本处理相关配置
        self.MAX_TEXT_LENGTH = 12000  # 单次处理的最大文本长度
        self.MAX_RETRIES = 3  # 减少重试次数，提高效率
        self.ENABLE_MULTI_ROUND = True  # 启用多轮改写策略

        # 改写策略配置
        self.rewrite_strategies = {
            'synonym_replacement': True,    # 同义词替换
            'sentence_restructure': True,   # 句式重构
            'paragraph_reorder': True,      # 段落重组
            'style_variation': True,        # 语言风格变化
            'back_translation': False       # 翻译回译（可选）
        }

        # 改写幅度配置
        self.rewrite_ratio = {
            'min': 70,  # 提高最小改写幅度
            'max': 90   # 提高最大改写幅度
        }

        # 模型配置
        self.MODEL_CONFIG = {
            'temperature': {
                'creative': 0.9,        # 创意改写
                'balanced': 0.7,        # 平衡模式
                'conservative': 0.5,    # 保守模式
                'min': 0.3,            # 最小温度
                'step': 0.1            # 每次重试减少的温度值
            },
            'max_tokens': 8000,    # DeepSeek API最大token数限制为8192，设置为8000安全
            'max_length': 6000     # 相应减少最大文本长度
        }

        # 初始化提示词
        self._init_prompts()

    def _load_config(self):
        """加载配置文件"""
        try:
            # 从JSON文件加载配置
            config_file = 'config.json'
            if not os.path.exists(config_file):
                # 兼容旧的api_config.json
                config_file = 'api_config.json'

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 如果有api_configs，直接使用
                if 'api_configs' in config:
                    print("成功加载JSON配置文件")
                    return config['api_configs']

                # 兼容旧格式，构建api_configs
                api_key = config.get('api_key', '')
                return {
                    'primary': {
                        'base_url': "https://api.deepseek.com/v1",
                        'api_key': api_key,
                        'model': "deepseek-chat"
                    }
                }
        except Exception as e:
            print(f"加载配置文件失败: {e}")

        # 如果加载失败，返回默认配置
        return {
            'primary': {
                'base_url': "https://api.deepseek.com/v1",
                'api_key': "",
                'model': "deepseek-chat"
            }
        }

    def _load_folder_config(self):
        """加载文件夹配置"""
        try:
            # 优先加载新的config.json
            config_file = 'config.json'
            if not os.path.exists(config_file):
                # 兼容旧的api_config.json
                config_file = 'api_config.json'

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return {
                        'input_path': config.get('input_path', config.get('input_folder', '')),  # 兼容旧版本
                        'input_mode': config.get('input_mode', 'folder'),
                        'output_folder': config.get('output_folder', ''),
                        'output_format': config.get('output_format', 'auto'),
                        'provider': config.get('provider', 'deepseek'),
                        'selected_model': config.get('selected_model', 'deepseek-chat')
                    }
        except Exception as e:
            print(f"加载文件夹配置失败: {e}")
            if hasattr(self, 'gui') and self.gui:
                self.gui.add_log(f"加载文件夹配置失败: {e}")

        return {'input_path': '', 'input_mode': 'folder', 'output_folder': '', 'output_format': 'auto'}

    def _build_http_client(self, use_proxy: bool = False):
        """构建HTTP客户端；当use_proxy为True时，使用本地代理127.0.0.1:7890"""
        import httpx
        if use_proxy:
            proxies = {
                "http://": "http://127.0.0.1:7890",
                "https://": "http://127.0.0.1:7890",
            }
            return httpx.Client(timeout=60.0, proxies=proxies)
        else:
            return httpx.Client(timeout=60.0)

    def _init_client(self, api_name, use_proxy: bool = False):
        """初始化API客户端；支持可选代理"""
        try:
            config = self.api_configs[api_name]
            if not config['api_key']:
                print(f"警告: {api_name} API密钥未配置")
                return None

            from openai import OpenAI

            http_client = self._build_http_client(use_proxy)

            return OpenAI(
                base_url=config['base_url'],
                api_key=config['api_key'],
                http_client=http_client
            )
        except Exception as e:
            print(f"初始化{api_name} API客户端失败: {e}")
            return None

    def switch_api(self, api_name):
        """切换API服务商；失败时自动尝试本地代理再重试一次"""
        if api_name not in self.api_configs:
            print(f"切换到 {api_name} API 失败：配置不存在")
            return False

        # 先尝试直连
        client = self._init_client(api_name, use_proxy=False)
        if client:
            self.current_api = api_name
            self.client = client
            print(f"已切换到 {api_name} API（直连）")
            return True

        # 直连失败，尝试本地代理
        print("直连失败，尝试使用本地代理 127.0.0.1:7890 ...")
        client = self._init_client(api_name, use_proxy=True)
        if client:
            self.current_api = api_name
            self.client = client
            print(f"已切换到 {api_name} API（本地代理）")
            return True

        print(f"切换到 {api_name} API 失败（直连和代理均不可用）")
        return False

    def test_api_connection(self, api_name):
        """测试API连接"""
        if api_name not in self.api_configs:
            return False, "API配置不存在"

        config = self.api_configs[api_name]
        if not config['api_key']:
            return False, "API密钥未配置"

        try:
            from openai import OpenAI
            client = OpenAI(
                base_url=config['base_url'],
                api_key=config['api_key']
            )

            # 发送测试请求
            response = client.chat.completions.create(
                model=config['model'],
                messages=[{"role": "user", "content": "测试连接"}],
                max_tokens=10,
                temperature=0.1
            )

            return True, "连接成功"
        except Exception as e:
            return False, f"连接失败: {str(e)}"

    def check_api_status(self):
        """检查所有API状态"""
        print("\n检查API连接状态...")
        available_apis = []

        for api_name in self.api_configs:
            success, message = self.test_api_connection(api_name)
            status = "✅" if success else "❌"
            print(f"{status} {api_name}: {message}")

            if success:
                available_apis.append(api_name)

        if not available_apis:
            print("\n⚠️  警告：没有可用的API！")
            print("请检查config.py中的API配置")
            return False
        else:
            print(f"\n✅ 发现 {len(available_apis)} 个可用API: {', '.join(available_apis)}")
            return True

    def _init_prompts(self):
        """初始化提示词配置"""
        # ====== 提示词配置 ======
        # 多轮改写策略提示词
        self.multi_round_prompts = {
            'round1_creative': """你是一位专业的内容改写专家。请对以下文本进行创意性改写，要求：
1. 大幅度改变表达方式，使用不同的词汇和句式
2. 保持原文的核心意思和信息完整性
3. 让语言更加生动有趣，提高可读性
4. 改写幅度要达到70-90%，确保与原文有明显差异
5. 只提供改写后的内容，无需解释

请改写以下文本：""",

            'round2_polish': """你是一位文本润色专家。请对以下文本进行精细化润色，要求：
1. 修正所有语法错误和错别字
2. 优化语言表达，使其更加流畅自然
3. 保持改写后的风格和特色
4. 确保逻辑清晰，表达准确
5. 只提供润色后的内容，无需解释

请润色以下文本：""",

            'synonym_rewrite': """你是一位同义词改写专家。请使用同义词替换和句式重构来改写文本，要求：
1. 大量使用同义词替换关键词汇
2. 改变句子结构和表达方式
3. 保持原文意思不变
4. 让文本读起来像全新的内容
5. 只提供改写后的内容，无需解释

请改写以下文本：""",

            'back_translation': """你是一位专业的翻译专家。请对以下中文文本进行翻译回译改写：
1. 先将中文翻译成英文
2. 再将英文翻译回中文
3. 确保回译后的中文表达自然流畅
4. 保持原文的核心意思不变
5. 通过翻译过程自然地改变表达方式

请直接输出最终的中文文本，不要显示翻译过程："""
        }

        # 英文基督教内容提示词
        self.en_christian_prompt = """You are a professional Christian content editor. Your task is to rewrite the given English text with the following requirements:

1. Content Requirements:
   - Maintain biblical accuracy
   - Make the language more vivid and easy to understand
2. Improve the clarity and fluency of the article, which can be adjusted from the original text, and must be significantly different from the original text
3. Correct all typos and grammatical errors, without any content in parentheses
4. Only provide the rewritten content, without explanation"""

        # 中文基督教内容提示词
        self.cn_christian_prompt = """你是一位基督教专家。你的任务是改写给定的文本，要求：
1. 使语言更加生动易懂
2. 改善文章的清晰度和流畅性，可以对原文进行删减和调整，要和原文明显不一样
3. 修正所有错别字和语法错误，不要有括号内容
4. 只提供改写后的内容，无需解释"""

        # 通用内容提示词
        self.general_prompt = """你是一位内容编辑专家。你的任务是改写给定的文本，要求：
1. 使语言更加生动易懂
2. 改善文章的清晰度和流畅性，可对原文进行删减和调整，要和原文明显不一样，相似度不能高于5%
3. 修正所有错别字和语法错误，不要有括号内容
4. 只提供改写后的内容，无需解释"""

        # 提取主题改写提示词
        self.topic_extraction_prompt = """你是一位内容编辑专家。你的任务是改写给定的文本，要求：
1. 将文本的大纲进行提取，要保证日期时间和引用内容的准确性
2. 根据文章大纲进行重写，文章逻辑要清晰流畅
3. 不要有错别字和语法错误，不要有括号内容
4. 只提供改写后的内容，无需解释"""

        # 西班牙语翻译提示词
        self.spanish_prompt = """Por favor, traduce el siguiente texto al español. Requisitos:
1. Mantén el significado original y todos los detalles del texto
2. Asegúrate de que la traducción sea precisa y completa
3. Mantén un tono profesional y natural
4. Conserva toda la terminología técnica y específica
5. Mantén la misma estructura y organización del texto original
6. La traducción debe ser fiel al original, sin omitir ni agregar información
7. Utiliza expresiones naturales en español manteniendo el mismo nivel de formalidad

Importante: La traducción debe ser detallada y mantener todos los matices del texto original."""

        # 中文到英文翻译提示词
        self.chinese_to_english_prompt = """You are a professional translator. Your task is to translate the given Chinese text into English with the following requirements:

1. Translation Requirements:
   - Make the language vivid and easy to understand
   - Improve the clarity and fluency while maintaining the original meaning
   - Use natural and idiomatic English expressions
2. Content Requirements:
   - Maintain accuracy of all details and technical terms
   - Remove any content in parentheses or special symbols
   - Fix any typos or grammatical errors
3. Only provide the translated content in English, without any explanations or notes"""

        # 英文到中文翻译提示词
        self.english_to_chinese_prompt = """你是一位翻译改写专家。你的任务是将给定的文本翻译并微调，要求：
1. 使翻译后的语言更加生动易懂 符合中文语境
2. 改善文章的清晰度和流畅性
3. 修正所有错别字和语法错误，不要有括号内容
4. 只提供改写后的内容，无需解释"""

        # 英文本地化改写提示词
        self.localization_prompt = """You are a professional content editor. Your task is to rewrite the given Chinese text with the following requirements:

1. Content Requirements:
   - Replace Chinese place names and person names noun concepts in the text,with common American names for better understanding by American audience
   - Make the language more vivid and easy to understand
2. Improve the clarity and fluency of the article, which can be adjusted from the original text
3. Correct all typos and grammatical errors, without any content in parentheses
4. 在满足上述条件后，将中文全部翻译成英文
5. Only provide the rewritten content, without explanation"""

        # 第二轮改写提示词
        self.general_prompt_round2 = """你是一位内容编辑专家。你的任务是将给定的文本进行纠错，要求：
1. 使语言更加易懂，修正所有错别字和语法错误
2. 只提供改写后的内容，无需解释
"""

        # 第二轮基督教内容提示词
        self.cn_christian_prompt_round2 = """你是一位基督教专家。你的任务是纠错给定的文本，要求：
1. 使语言更加易懂，修正所有错别字和语法错误
2. 只提供改写后的内容，无需解释
"""

    def detect_encoding(self, file_path):
        """检测文件编码"""
        try:
            import chardet

            with open(file_path, 'rb') as file:
                raw_data = file.read()
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']

                print(f"检测到编码: {encoding} (置信度: {confidence:.2f})")
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(f"检测到编码: {encoding} (置信度: {confidence:.2f})")

                # 如果置信度太低，尝试常见编码
                if confidence < 0.7:
                    print("置信度较低，尝试常见编码...")
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.add_log("置信度较低，尝试常见编码...")

                    common_encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5', 'latin1']
                    for enc in common_encodings:
                        try:
                            raw_data.decode(enc)
                            print(f"成功使用编码: {enc}")
                            if hasattr(self, 'gui') and self.gui:
                                self.gui.add_log(f"成功使用编码: {enc}")
                            return enc
                        except:
                            continue

                return encoding

        except ImportError:
            print("未安装chardet库，使用备用方法检测编码...")
            if hasattr(self, 'gui') and self.gui:
                self.gui.add_log("未安装chardet库，使用备用方法检测编码...")

            # 备用方法：尝试常见编码
            common_encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5', 'latin1']

            for encoding in common_encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        file.read()
                    print(f"检测到编码: {encoding}")
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.add_log(f"检测到编码: {encoding}")
                    return encoding
                except UnicodeDecodeError:
                    continue

            print("无法检测文件编码，使用默认编码 utf-8")
            if hasattr(self, 'gui') and self.gui:
                self.gui.add_log("无法检测文件编码，使用默认编码 utf-8")
            return 'utf-8'

        except Exception as e:
            print(f"编码检测失败: {e}")
            if hasattr(self, 'gui') and self.gui:
                self.gui.add_log(f"编码检测失败: {e}")
            return 'utf-8'

    def read_file(self, file_path):
        """读取文件内容，支持txt和docx格式，自动检测编码"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.txt':
                # 检测文件编码
                encoding = self.detect_encoding(file_path)

                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read().strip()
                    return content
                except UnicodeDecodeError as e:
                    print(f"使用 {encoding} 编码读取失败: {e}")
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.add_log(f"使用 {encoding} 编码读取失败，尝试其他方法...")

                    print("尝试使用 utf-8 with errors='ignore'...")
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read().strip()
                            print("⚠️ 使用忽略错误模式读取，可能丢失部分字符")
                            if hasattr(self, 'gui') and self.gui:
                                self.gui.add_log("⚠️ 使用忽略错误模式读取，可能丢失部分字符")
                            return content
                    except Exception as e2:
                        print(f"忽略错误模式也失败: {e2}")
                        if hasattr(self, 'gui') and self.gui:
                            self.gui.add_log(f"忽略错误模式也失败: {e2}")
                        return None

            elif file_ext == '.docx':
                # 读取docx文件
                doc = Document(file_path)
                paragraphs = []
                for para in doc.paragraphs:
                    text = para.text.strip()
                    if text:  # 只添加非空段落
                        paragraphs.append(text)
                return '\n'.join(paragraphs)
            else:
                error_msg = f"不支持的文件格式: {file_ext}"
                print(error_msg)
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(error_msg)
                return None

        except Exception as e:
            error_msg = f"读取文件时出错: {str(e)}"
            print(error_msg)
            if hasattr(self, 'gui') and self.gui:
                self.gui.add_log(error_msg)
            return None

    def save_to_docx(self, text, file_path):
        """保存内容到docx文件"""
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(file_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 根据文件扩展名决定保存格式
            _, ext = os.path.splitext(file_path)

            if ext.lower() == '.txt':
                # 保存为txt文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                print(f"文本文件已保存: {file_path}")
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(f"文本文件已保存: {os.path.basename(file_path)}")
            else:
                # 保存为docx文件
                doc = Document()
                # 将文本按换行符分割成段落
                paragraphs = text.split('\n')
                for para in paragraphs:
                    if para.strip():  # 只添加非空段落
                        doc.add_paragraph(para.strip())
                doc.save(file_path)
                print(f"Word文档已保存: {file_path}")
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(f"Word文档已保存: {os.path.basename(file_path)}")

            return True
        except Exception as e:
            error_msg = f"保存文件时出错: {str(e)}"
            print(error_msg)
            if hasattr(self, 'gui') and self.gui:
                self.gui.add_log(error_msg)
            return False

    def get_unique_filename(self, base_path):
        """获取唯一的文件名，如果文件存在则在文件名后加上数字"""
        try:
            # 使用配置的输出文件夹
            output_folder = self.folder_config.get('output_folder', '')
            if not output_folder:
                print('错误：未配置输出文件夹')
                return None

            # 确保输出文件夹存在
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            # 获取原始文件名和扩展名
            filename = os.path.basename(base_path)
            name, ext = os.path.splitext(filename)

            # 根据用户选择的输出格式生成文件路径
            counter = 1

            # 获取输出格式设置
            if hasattr(self, 'folder_config') and self.folder_config:
                format_choice = self.folder_config.get('output_format', 'auto')
            else:
                format_choice = 'auto'  # 默认自动格式

            # 确定输出文件扩展名
            if format_choice == 'txt':
                output_ext = '.txt'
            elif format_choice == 'docx':
                output_ext = '.docx'
            else:  # auto - 保持原格式
                output_ext = ext

            # 生成文件路径
            new_path = os.path.join(output_folder, f"改写_{name}{output_ext}")
            while os.path.exists(new_path):
                new_path = os.path.join(output_folder, f"改写_{name}_{counter}{output_ext}")
                counter += 1

            return new_path
        except Exception as e:
            print(f"生成文件名时出错: {str(e)}")
            return None

    def get_files(self):
        """获取输入路径中的txt和docx文件"""
        try:
            # 使用配置的输入路径
            input_path = self.folder_config.get('input_path', '')
            input_mode = self.folder_config.get('input_mode', 'folder')

            if not input_path:
                error_msg = '错误：未配置输入路径'
                print(error_msg)
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(error_msg)
                    self.gui.add_log('请在GUI界面中选择输入文件或文件夹')
                else:
                    print('请在GUI界面中选择输入文件或文件夹')
                return []

            if not os.path.exists(input_path):
                error_msg = f'错误：输入路径不存在: {input_path}'
                print(error_msg)
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(error_msg)
                return []

            files = []

            if input_mode == 'file':
                # 单文件模式
                if input_path.endswith('.docx') or input_path.endswith('.txt'):
                    filename = os.path.basename(input_path)
                    files.append((filename, input_path))
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.add_log(f"选择单个文件: {filename}")
                else:
                    error_msg = f'错误：不支持的文件格式: {input_path}'
                    print(error_msg)
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.add_log(error_msg)
                    return []
            else:
                # 文件夹模式
                if not os.path.isdir(input_path):
                    error_msg = f'错误：输入路径不是文件夹: {input_path}'
                    print(error_msg)
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.add_log(error_msg)
                    return []

                # 获取文件夹中的所有非隐藏的txt和docx文件
                for file in os.listdir(input_path):
                    # 跳过隐藏文件和临时文件
                    if (not file.startswith('.') and not file.startswith('~')):
                        if file.endswith('.docx') or file.endswith('.txt'):
                            file_path = os.path.join(input_path, file)
                            if os.path.isfile(file_path):
                                files.append((file, file_path))

            if not files:
                error_msg = f'在 {input_path} 中没有找到.txt或.docx文件'
                print(error_msg)
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(error_msg)
                    self.gui.add_log('请确保输入路径包含要处理的文件')
                else:
                    print('请将要处理的文件放入输入路径')
                return []

            # 按自然排序对文件进行排序
            files.sort(key=lambda x: self.natural_sort_key(x[0]))

            # 显示找到的文件
            log_msg = f"找到 {len(files)} 个文件："
            print(f"\n{log_msg}")
            if hasattr(self, 'gui') and self.gui:
                self.gui.add_log(log_msg)

            for i, (filename, _) in enumerate(files, 1):
                file_type = "Word文档" if filename.endswith('.docx') else "文本文件"
                file_info = f"{i}. {filename} ({file_type})"
                print(file_info)
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(file_info)
            print()

            return files
        except Exception as e:
            error_msg = f"获取文件列表时出错: {str(e)}"
            print(error_msg)
            if hasattr(self, 'gui') and self.gui:
                self.gui.add_log(error_msg)
            traceback.print_exc()  # 打印详细的错误信息
            return []

    def count_tokens(self, text):
        """使用tiktoken准确计算token数量"""
        if text is None:
            return 0
        try:
            encoding = tiktoken.get_encoding("cl100k_base")
            return len(encoding.encode(str(text)))  # 确保text是字符串
        except Exception as e:
            print(f"Token计算失败: {str(e)}")
            # 如果tiktoken失败，使用简单估算（每4个字符约1个token）
            return len(str(text)) // 4

    def calculate_cost(self, input_tokens, output_tokens, api_name='primary'):
        """计算DeepSeek API调用成本 - 使用最新价格表"""
        if input_tokens is None or output_tokens is None:
            return 0

        import datetime
        current_hour = datetime.datetime.now().hour

        # DeepSeek API最新定价（每1M token，单位：元）
        if 0 <= current_hour < 8 or current_hour >= 24:
            # 优惠时段 (00:30-08:30)
            input_price_cache_hit = 0.25 / 1000000      # 缓存命中
            input_price_cache_miss = 1.0 / 1000000      # 缓存未命中
            output_price = 4.0 / 1000000                # 输出价格
        else:
            # 标准时段 (08:30-00:30)
            input_price_cache_hit = 0.5 / 1000000       # 缓存命中
            input_price_cache_miss = 2.0 / 1000000      # 缓存未命中
            output_price = 8.0 / 1000000                # 输出价格

        # 假设50%的输入token是缓存命中（实际情况可能不同）
        cache_hit_ratio = 0.5
        input_cost = (input_tokens * cache_hit_ratio * input_price_cache_hit +
                     input_tokens * (1 - cache_hit_ratio) * input_price_cache_miss)
        output_cost = output_tokens * output_price

        return input_cost + output_cost

    def split_text_by_length(self, text, max_length=None):
        """将文本按长度分段"""
        if max_length is None:
            max_length = self.MODEL_CONFIG['max_length']
            
        segments = []
        current_segment = ""
        
        for paragraph in text.split('\n'):
            if len(current_segment) + len(paragraph) + 1 <= max_length:
                current_segment += (paragraph + '\n')
            else:
                if current_segment:
                    segments.append(current_segment.strip())
                current_segment = paragraph + '\n'
        
        if current_segment:
            segments.append(current_segment.strip())
            
        return segments

    def process_segment(self, text, mode, round_type='round1_creative'):
        """处理单个文本段落 - 使用新的多轮改写策略"""
        import time

        # 确保mode是整数
        mode = int(mode)

        # 生成提示词
        if mode == 1:  # 多轮改写模式
            if round_type in self.multi_round_prompts:
                prompt = f"{self.multi_round_prompts[round_type]}\n\n{text}"
            else:
                prompt = f"{self.general_prompt}\n\n{text}"
        elif mode == 2:  # 中文到英文翻译
            prompt = f"{self.chinese_to_english_prompt}\n\n请翻译以下文本：\n\n{text}"
        elif mode == 3:  # 英文本地化人地名改写
            prompt = f"{self.localization_prompt}\n\n请改写以下文本：\n\n{text}"
        elif mode == 4:  # 英文到中文翻译
            prompt = f"{self.english_to_chinese_prompt}\n\n请翻译以下英文文本：\n\n{text}"
        elif mode == 5:  # 提取主题改写
            prompt = f"{self.topic_extraction_prompt}\n\n请改写以下文本：\n\n{text}"
        else:
            print(f"错误：不支持的处理模式 {mode}")
            return None, 0

        # 使用所选提供方API（deepseek/openai）
        api_name = self.current_api
        if api_name not in self.api_configs or not self.api_configs[api_name].get('api_key'):
            print("❌ API密钥未配置，请先在GUI中设置")
            return None, 0

        try:
            # 确保使用正确的API
            if self.current_api != api_name:
                if not self.switch_api(api_name):
                    return None, 0

            # 根据模式选择温度
            if mode == 1:  # 创意改写模式
                current_temp = self.MODEL_CONFIG['temperature']['creative']
            elif mode in [2, 4]:  # 翻译模式
                current_temp = self.MODEL_CONFIG['temperature']['balanced']
            else:
                current_temp = self.MODEL_CONFIG['temperature']['conservative']

            print(f"\n使用DeepSeek API处理中...")
            start_time = time.time()

            # 调用API
            response = self.client.chat.completions.create(
                model=self.api_configs[api_name]['model'],
                messages=[{"role": "user", "content": prompt}],
                temperature=current_temp,
                max_tokens=self.MODEL_CONFIG['max_tokens']
            )

            process_time = time.time() - start_time
            print(f"处理完成，耗时: {process_time:.2f}秒")

            # 获取生成的内容
            content = response.choices[0].message.content.strip()

            # 计算token和成本
            input_tokens = response.usage.prompt_tokens
            output_tokens = response.usage.completion_tokens
            cost = self.calculate_cost(input_tokens, output_tokens, api_name)

            print(f"使用模型: {self.api_configs[api_name]['model']}")
            print(f"本次调用成本: ${cost:.4f}")
            print(f"Token使用 - 输入: {input_tokens}, 输出: {output_tokens}")

            # 更新GUI状态（线程安全）
            if hasattr(self, 'gui') and self.gui:
                # 使用after方法确保在主线程中更新GUI
                self.gui.root.after(0, lambda: self.gui.update_status_display(input_tokens, output_tokens, cost))
                self.gui.root.after(0, lambda: self.gui.add_log(f"API调用完成 - Token: {input_tokens + output_tokens}, 费用: ¥{cost:.4f}"))

            return content, cost

        except Exception as e:
            print(f"API 处理出错: {str(e)}")
            return None, 0

    def process_file(self, filepath, mode):
        """处理单个文件"""
        filename = os.path.basename(filepath)
        print(f"\n开始处理文件: {filepath}")
        total_cost = 0

        # 更新GUI状态（线程安全）
        if hasattr(self, 'gui') and self.gui:
            self.gui.root.after(0, lambda: self.gui.add_log(f"开始处理: {filename}"))
            self.gui.root.after(0, lambda: self.gui.update_status_display(current_file=filename, step="初始化"))

        # 检查文件
        if not os.path.exists(filepath):
            print("文件不存在")
            return False, 0, 0
            
        print(f"文件是否存在: {os.path.exists(filepath)}")
        print(f"文件是否可读: {os.access(filepath, os.R_OK)}")
        print(f"文件大小: {os.path.getsize(filepath)} 字节")
        
        try:
            print("\n尝试读取文件内容...")
            if hasattr(self, 'gui') and self.gui:
                self.gui.root.after(0, lambda: self.gui.add_log("正在读取文件内容..."))
                self.gui.root.after(0, lambda: self.gui.update_status_display(step="读取文件"))

            content = self.read_file(filepath)
            if not content:
                error_msg = "文件内容为空"
                print(error_msg)
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(f"错误: {error_msg}")
                return False, 0, 0
                
            original_chars = len(content)
            print(f"成功读取文件，字符数: {original_chars}")
            print(f"内容预览（前100字符）: {content[:100]}")
            
            # 处理逻辑 - 根据用户勾选的策略执行多轮改写
            if int(mode) == 1:  # 多轮改写模式
                print(f"\n开始多轮改写策略，文本长度: {len(content)} 字符")

                # 获取用户勾选的改写策略
                enabled_strategies = []
                if self.rewrite_strategies.get('synonym_replacement', False):
                    enabled_strategies.append(('同义词替换', 'synonym_rewrite'))
                if self.rewrite_strategies.get('sentence_restructure', False):
                    enabled_strategies.append(('句式重构', 'round1_creative'))
                if self.rewrite_strategies.get('paragraph_reorder', False):
                    enabled_strategies.append(('段落重组', 'round2_polish'))
                if self.rewrite_strategies.get('style_variation', False):
                    enabled_strategies.append(('风格变换', 'round1_creative'))
                if self.rewrite_strategies.get('back_translation', False):
                    enabled_strategies.append(('翻译回译', 'back_translation'))

                if not enabled_strategies:
                    print("⚠️ 没有启用任何改写策略，使用默认策略")
                    enabled_strategies = [('默认改写', 'round1_creative')]

                print(f"启用的改写策略: {', '.join([name for name, _ in enabled_strategies])}")
                if hasattr(self, 'gui') and self.gui:
                    self.gui.root.after(0, lambda: self.gui.add_log(f"启用的改写策略: {', '.join([name for name, _ in enabled_strategies])}"))

                current_content = content

                # 按顺序执行每个启用的策略
                for round_num, (strategy_name, strategy_type) in enumerate(enabled_strategies, 1):
                    print(f"\n第{round_num}轮：{strategy_name}...")
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.root.after(0, lambda sn=strategy_name, rn=round_num: self.gui.add_log(f"第{rn}轮：{sn}"))
                        self.gui.root.after(0, lambda sn=strategy_name, rn=round_num: self.gui.update_status_display(step=f"第{rn}轮：{sn}"))

                    segments = self.split_text_by_length(current_content)
                    round_segments = []

                    for i, segment in enumerate(segments, 1):
                        print(f"处理第 {i}/{len(segments)} 段 ({strategy_name})")
                        processed_text, cost = self.process_segment(segment, mode, strategy_type)
                        if processed_text:
                            round_segments.append(processed_text)
                            total_cost += cost
                        else:
                            print(f"第{round_num}轮处理第 {i} 段失败")
                            if hasattr(self, 'gui') and self.gui:
                                self.gui.add_log(f"第{round_num}轮处理第 {i} 段失败")
                            return False, total_cost, 0

                    current_content = "\n".join(round_segments)
                    print(f"第{round_num}轮完成，文本长度: {len(current_content)} 字符")

                final_content = current_content

                # 保存结果
                output_path = self.get_unique_filename(filepath)
                if output_path and self.save_to_docx(final_content, output_path):
                    print(f"\n多轮改写完成，结果已保存至: {output_path}")
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.add_log(f"✅ 文件已保存: {os.path.basename(output_path)}")
                    print(f"总成本: ${total_cost:.4f}")
                    return True, total_cost, len(final_content)

                return False, total_cost, 0
            
            # 分段处理
            segments = self.split_text_by_length(content)
            print(f"\n文本已分为 {len(segments)} 段")
            
            processed_segments = []
            for i, segment in enumerate(segments, 1):
                print(f"\n开始处理第 {i}/{len(segments)} 段:")
                processed_text, cost = self.process_segment(segment, mode)
                if processed_text:
                    processed_segments.append(processed_text)
                    total_cost += cost
                else:
                    print(f"处理第 {i} 段失败")
                    return False, total_cost, 0
            
            # 合并处理后的内容
            final_content = "\n".join(processed_segments)
            
            # 保存结果
            output_path = self.get_unique_filename(filepath)
            if output_path and self.save_to_docx(final_content, output_path):
                print(f"\n处理完成，结果已保存至: {output_path}")
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log(f"✅ 文件已保存: {os.path.basename(output_path)}")
                print(f"总成本: ${total_cost:.4f}")
                return True, total_cost, len(final_content)
            
            return False, total_cost, 0
            
        except Exception as e:
            print(f"处理文件时出错: {str(e)}")
            return False, total_cost, 0

    def select_mode(self, file_name):
        """为单个文件选择处理模式"""
        while True:
            print(f"\n请为文件 {file_name} 选择处理模式:")
            print("1. 智能多轮改写（创意改写+精细润色）")
            print("2. 中文到英文翻译")
            print("3. 英文本地化人地名改写")
            print("4. 英文到中文翻译")
            print("5. 提取主题改写")

            choice = input("\n请输入模式编号(1-5): ").strip()

            if choice in ['1', '2', '3', '4', '5']:
                print(f"已选择模式 {choice}")
                return choice
            else:
                print("无效的选择，请重试")

    def natural_sort_key(self, file_tuple):
        import re
        file_name = file_tuple[0]
        convert = lambda text: int(text) if text.isdigit() else text.lower()
        alphanum_key = lambda key: [convert(c) for c in re.split('([0-9]+)', key)]
        return alphanum_key(file_name)

    def process_files(self, files, modes):
        """处理多个文件"""
        results = []
        total_chars = 0
        total_rewritten_chars = 0
        total_cost = 0
        success_count = 0

        for i, (filename, filepath) in enumerate(files, 1):
            print(f"\n处理文件 {i}/{len(files)}: {filename}")
            try:
                mode = modes.get(filename)
                if not mode:
                    print(f"未找到文件 {filename} 的处理模式")
                    continue

                print(f"完整文件路径: {filepath}")
                print(f"选择的处理模式: {mode}")
                
                success, cost, chars = self.process_file(filepath, mode)
                
                if success:
                    success_count += 1
                    total_chars += chars
                    # 获取改写后的字符数
                    output_path = self.get_latest_output_path()
                    if output_path and os.path.exists(output_path):
                        try:
                            # 使用python-docx读取文件内容
                            doc = Document(output_path)
                            rewritten_text = '\n'.join([paragraph.text for paragraph in doc.paragraphs])
                            rewritten_chars = len(rewritten_text)
                            total_rewritten_chars += rewritten_chars
                            results.append({
                                'filename': filename,
                                'original_chars': chars,
                                'rewritten_chars': rewritten_chars,
                                'success': True
                            })
                        except Exception as e:
                            print(f"读取输出文件时出错: {str(e)}")
                            results.append({
                                'filename': filename,
                                'original_chars': chars,
                                'rewritten_chars': 0,
                                'success': False
                            })
                    else:
                        print(f"无法读取输出文件: {output_path}")
                        results.append({
                            'filename': filename,
                            'original_chars': chars,
                            'rewritten_chars': 0,
                            'success': False
                        })
                else:
                    results.append({
                        'filename': filename,
                        'original_chars': chars,
                        'rewritten_chars': 0,
                        'success': False
                    })
                
                total_cost += cost
                
            except Exception as e:
                print(f"处理文件时出错: {str(e)}")
                traceback.print_exc()
                results.append({
                    'filename': filename,
                    'original_chars': 0,
                    'rewritten_chars': 0,
                    'success': False
                })

        # 打印处理结果
        print("\n处理完成!")
        print(f"成功: {success_count}/{len(files)}")
        
        # 打印字符统计表格
        print("\n字符统计:")
        print("-" * 100)
        print(f"{'文件名':<40} {'原文字符':<16} {'改写字符':<16} {'变化比例':<12} {'状态':<8}")
        print("-" * 100)
        
        for result in results:
            filename = result['filename']
            original = result['original_chars']
            rewritten = result['rewritten_chars']
            ratio = (rewritten / original * 100) if original > 0 else 0.0
            status = "成功" if result['success'] else "失败"
            
            print(f"{filename:<40} {original:<16} {rewritten:<16} {ratio:.1f}% {status:<8}")
        
        print("-" * 100)
        print("总计:")
        print(f"原文总字符: {total_chars}")
        print(f"改写总字符: {total_rewritten_chars}")
        total_ratio = (total_rewritten_chars / total_chars * 100) if total_chars > 0 else 0.0
        print(f"总体变化比例: {total_ratio:.1f}%")
        print(f"总成本: ${total_cost:.4f}")

    def get_latest_output_path(self):
        """获取最新的输出文件路径"""
        try:
            rewrite_folder = os.path.join(os.path.expanduser("~/Desktop"), "改写")
            if not os.path.exists(rewrite_folder):
                return None
            
            files = [f for f in os.listdir(rewrite_folder) if f.endswith('.docx')]
            if not files:
                return None
                
            files.sort(key=lambda x: os.path.getmtime(os.path.join(rewrite_folder, x)), reverse=True)
            return os.path.join(rewrite_folder, files[0])
        except Exception as e:
            print(f"获取最新输出文件路径时出错: {str(e)}")
            return None

    def generate_prompt(self, text, mode):
        """根据模式生成提示词"""
        mode = int(mode)
        if mode == 1:  # 通用模式
            if hasattr(self, 'is_second_round') and self.is_second_round:
                return f"{self.general_prompt_round2}\n\n请改写以下文本：\n\n{text}"
            else:
                return self.general_prompt + "\n\n" + text
        elif mode == 2:  # 中文到英文翻译
            return f"{self.chinese_to_english_prompt}\n\nPlease translate the following Chinese text into English:\n\n{text}"
        elif mode == 3:  # 英文本地化人地名改写
            return f"{self.localization_prompt}\n\nPlease rewrite the following text:\n\n{text}"
        elif mode == 4:  # 英文到中文翻译
            return f"{self.english_to_chinese_prompt}\n\n请翻译以下英文文本：\n\n{text}"
        elif mode == 5:  # 提取主题改写
            return f"{self.topic_extraction_prompt}\n\n请改写以下文本：\n\n{text}"

    def validate_rewrite_quality(self, original_text, rewritten_text):
        """验证改写质量"""
        if not rewritten_text or not original_text:
            return False, "改写结果为空"

        original_length = len(original_text)
        rewritten_length = len(rewritten_text)

        # 检查长度比例
        min_length = int(original_length * (self.rewrite_ratio['min'] / 100))
        max_length = int(original_length * (self.rewrite_ratio['max'] / 100))

        if rewritten_length < min_length:
            return False, f"改写内容过短 ({rewritten_length}/{min_length})"
        elif rewritten_length > max_length:
            return False, f"改写内容过长 ({rewritten_length}/{max_length})"

        # 简单的相似度检查（避免完全相同的内容）
        if original_text.strip() == rewritten_text.strip():
            return False, "改写内容与原文完全相同"

        return True, f"改写质量良好 ({rewritten_length} 字符，{(rewritten_length/original_length*100):.1f}%)"

    def run(self):
        """主运行方法"""
        # 如果有GUI引用，说明是从GUI启动的
        if hasattr(self, 'gui') and self.gui:
            return self.run_with_gui()
        else:
            return self.run_console()

    def run_with_gui(self):
        """GUI模式运行"""
        try:
            # 获取所有支持的文件
            files = self.get_files()
            if not files:
                if hasattr(self, 'gui') and self.gui:
                    self.gui.add_log("没有找到可处理的文件")
                return

            # 更新总文件数
            if hasattr(self, 'gui') and self.gui:
                self.gui.update_status_display(total=len(files))
                self.gui.add_log(f"准备处理 {len(files)} 个文件")

            # 处理所有文件（GUI模式下自动处理所有文件，使用模式1）
            mode = "1"  # 默认使用智能多轮改写

            for i, (filename, filepath) in enumerate(files):
                if hasattr(self, 'gui') and self.gui:
                    self.gui.update_status_display(progress=i, current_file=filename, step="开始处理")
                    self.gui.add_log(f"处理文件 {i+1}/{len(files)}: {filename}")

                success, total_cost, _ = self.process_file(filepath, mode)

                if success:
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.add_log(f"✅ {filename} 处理完成，费用: ¥{total_cost:.4f}")
                else:
                    if hasattr(self, 'gui') and self.gui:
                        self.gui.add_log(f"❌ {filename} 处理失败")

            # 处理完成
            if hasattr(self, 'gui') and self.gui:
                self.gui.update_status_display(progress=len(files), step="全部完成")
                self.gui.add_log("🎉 所有文件处理完成！")

        except Exception as e:
            if hasattr(self, 'gui') and self.gui:
                self.gui.add_log(f"处理过程中出现错误: {e}")
            print(f"处理错误: {e}")

    def run_console(self):
        """控制台模式运行"""
        while True:
            # 获取所有支持的文件
            files = self.get_files()
            if not files:
                return

            # 显示当前配置并让用户选择是否启用多轮改写
            print("\n是否启用多轮改写策略? (y/n)")
            print("多轮改写：第一轮创意性改写 + 第二轮精细化润色，效果更好但成本更高")
            self.ENABLE_MULTI_ROUND = input().lower() == 'y'
            print(f"已{'启用' if self.ENABLE_MULTI_ROUND else '禁用'}多轮改写策略")
            
            # 获取用户选择
            choice = input("\n请输入要处理的文件编号（多个文件用空格分隔，输入q退出）: ").strip()
            if choice.lower() == 'q':
                break

            try:
                # 解析用户选择
                selected_indices = [int(x) - 1 for x in choice.split()]
                selected_files = []
                modes = {}

                # 验证选择的有效性
                for idx in selected_indices:
                    if 0 <= idx < len(files):
                        selected_files.append(files[idx])
                    else:
                        print(f"无效的文件编号: {idx + 1}")
                        continue

                if not selected_files:
                    print("未选择任何文件")
                    continue

                print("\n为每个文件选择处理模式...")
                print("\n文件 1/1\n")

                # 为每个选中的文件选择处理模式
                for filename, _ in selected_files:
                    while True:
                        print(f"\n为文件 {filename} 选择处理模式：")
                        print("1. 智能多轮改写（创意改写+精细润色）")
                        print("2. 中文到英文翻译")
                        print("3. 英文本地化人地名改写")
                        print("4. 英文到中文翻译")
                        print("5. 提取主题改写")
                        print("q. 跳过此文件")

                        mode = input("\n请输入模式编号（1-5，或q跳过）: ").strip()
                        if mode.lower() == 'q':
                            break
                        if mode in ['1', '2', '3', '4', '5']:
                            modes[filename] = mode  # 使用filename作为键
                            break
                        print("无效的选择，请重试")

                # 显示选择的文件和模式
                print("\n已选择 {} 个文件处理，{} 个文件跳过".format(
                    len(modes), len(selected_files) - len(modes)))

                if modes:
                    print("\n选择的处理模式：")
                    for filename, _ in selected_files:
                        if filename in modes:
                            print(f"{filename}: {modes[filename]}")

                    # 确认开始处理
                    if input("\n确认开始处理？(y/n): ").lower() == 'y':
                        self.process_files(selected_files, modes)
                        break  # 处理完成后退出循环
                    else:
                        print("已取消处理")

            except ValueError:
                print("输入无效，请重试")
            except Exception as e:
                print(f"发生错误: {str(e)}")
                traceback.print_exc()


def main():
    """主函数"""
    # 直接打开GUI界面，不再检查配置
    gui = APIConfigGUI()
    gui.run()


if __name__ == "__main__":
    main()