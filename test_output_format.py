#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输出格式选择功能
"""

def test_gui_output_format():
    """测试GUI输出格式选择"""
    print("测试GUI输出格式选择...")
    
    try:
        from main import APIConfigGUI
        
        gui = APIConfigGUI()
        
        # 检查输出格式变量是否存在
        if hasattr(gui, 'output_format'):
            print("✅ 输出格式变量存在")
            
            # 测试默认值
            default_value = gui.output_format.get()
            print(f"✅ 默认输出格式: {default_value}")
            
            # 测试设置不同格式
            test_formats = ['auto', 'txt', 'docx']
            for fmt in test_formats:
                gui.output_format.set(fmt)
                current_value = gui.output_format.get()
                if current_value == fmt:
                    print(f"✅ 设置格式 {fmt}: 成功")
                else:
                    print(f"❌ 设置格式 {fmt}: 失败")
                    gui.root.destroy()
                    return False
            
            gui.root.destroy()
            return True
        else:
            print("❌ 输出格式变量不存在")
            gui.root.destroy()
            return False
            
    except Exception as e:
        print(f"❌ GUI输出格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_save_load():
    """测试配置保存和加载"""
    print("\n测试配置保存和加载...")
    
    try:
        import json
        import os
        
        # 创建测试配置
        test_config = {
            'input_path': 'C:/test/input.txt',
            'input_mode': 'file',
            'output_folder': 'C:/test/output',
            'output_format': 'txt',
            'api_configs': {
                'primary': {
                    'base_url': "https://api.deepseek.com/v1",
                    'api_key': 'test-key',
                    'model': "deepseek-chat"
                }
            }
        }
        
        # 保存测试配置
        with open('test_config.json', 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        
        print("✅ 测试配置已保存")
        
        # 备份原配置
        original_config = None
        if os.path.exists('config.json'):
            with open('config.json', 'r', encoding='utf-8') as f:
                original_config = f.read()
            os.rename('config.json', 'config_backup_temp.json')
        
        # 使用测试配置
        os.rename('test_config.json', 'config.json')
        
        # 测试加载
        from main import APIConfigGUI
        gui = APIConfigGUI()
        
        # 检查是否正确加载
        loaded_format = gui.output_format.get()
        if loaded_format == 'txt':
            print("✅ 输出格式加载成功")
            result = True
        else:
            print(f"❌ 输出格式加载失败，期望: txt，实际: {loaded_format}")
            result = False
        
        gui.root.destroy()
        
        # 恢复原配置
        os.remove('config.json')
        if original_config:
            with open('config.json', 'w', encoding='utf-8') as f:
                f.write(original_config)
            os.remove('config_backup_temp.json')
        
        return result
        
    except Exception as e:
        print(f"❌ 配置保存加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_filename_generation():
    """测试文件名生成"""
    print("\n测试文件名生成...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 模拟不同的配置
        test_cases = [
            {
                'config': {
                    'output_folder': 'C:/test/output',
                    'output_format': 'auto'
                },
                'input_file': 'C:/test/document.txt',
                'expected_ext': '.txt'
            },
            {
                'config': {
                    'output_folder': 'C:/test/output',
                    'output_format': 'docx'
                },
                'input_file': 'C:/test/document.txt',
                'expected_ext': '.docx'
            },
            {
                'config': {
                    'output_folder': 'C:/test/output',
                    'output_format': 'txt'
                },
                'input_file': 'C:/test/document.docx',
                'expected_ext': '.txt'
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            rewriter.folder_config = case['config']
            
            result_path = rewriter.get_unique_filename(case['input_file'])
            
            if result_path:
                import os
                _, actual_ext = os.path.splitext(result_path)
                expected_ext = case['expected_ext']
                
                if actual_ext == expected_ext:
                    print(f"✅ 测试用例 {i}: {case['config']['output_format']} -> {actual_ext}")
                else:
                    print(f"❌ 测试用例 {i}: 期望 {expected_ext}，实际 {actual_ext}")
                    return False
            else:
                print(f"❌ 测试用例 {i}: 文件名生成失败")
                return False
        
        print("✅ 文件名生成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件名生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_saving():
    """测试文件保存"""
    print("\n测试文件保存...")
    
    try:
        from main import TextRewriter
        import os
        import tempfile
        
        rewriter = TextRewriter()
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            test_content = "这是一个测试文档。\n包含多行内容。\n用于测试文件保存功能。"
            
            # 测试保存为txt
            txt_path = os.path.join(temp_dir, "test.txt")
            if rewriter.save_to_docx(test_content, txt_path):
                if os.path.exists(txt_path):
                    with open(txt_path, 'r', encoding='utf-8') as f:
                        saved_content = f.read()
                    if saved_content == test_content:
                        print("✅ txt文件保存成功")
                    else:
                        print("❌ txt文件内容不匹配")
                        return False
                else:
                    print("❌ txt文件未创建")
                    return False
            else:
                print("❌ txt文件保存失败")
                return False
            
            # 测试保存为docx
            docx_path = os.path.join(temp_dir, "test.docx")
            if rewriter.save_to_docx(test_content, docx_path):
                if os.path.exists(docx_path):
                    print("✅ docx文件保存成功")
                else:
                    print("❌ docx文件未创建")
                    return False
            else:
                print("❌ docx文件保存失败")
                return False
        
        print("✅ 文件保存测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("输出格式选择功能测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 4
    
    try:
        # 1. 测试GUI输出格式选择
        if test_gui_output_format():
            success_count += 1
        
        # 2. 测试配置保存和加载
        if test_config_save_load():
            success_count += 1
        
        # 3. 测试文件名生成
        if test_filename_generation():
            success_count += 1
        
        # 4. 测试文件保存
        if test_file_saving():
            success_count += 1
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 输出格式选择功能正常！")
        print("\n功能特性:")
        print("• 自动格式：保持原文件格式")
        print("• 文本格式：强制输出为.txt文件")
        print("• Word格式：强制输出为.docx文件")
        print("• 配置自动保存和加载")
        print("• 文件名正确生成")
    else:
        print("⚠️  部分测试失败")

if __name__ == "__main__":
    main()
