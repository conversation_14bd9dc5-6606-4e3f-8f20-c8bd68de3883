# 输出格式选择功能说明

## 🎯 新增功能

在输出文件夹选择下方，新增了**输出格式选择**功能，用户可以自由选择输出文件的格式。

## 📋 界面布局

```
输入输出配置
├── 输入模式: ○ 选择文件夹  ○ 选择单个文件
├── 输入路径: [________________] [选择文件夹]
├── 输出文件夹: [________________] [选择文件夹]
└── 输出格式: ○ 自动（保持原格式） ○ 文本文件(.txt) ○ Word文档(.docx)
```

## 🔧 功能选项

### 1. **自动（保持原格式）** - 默认选项
- 输入txt文件 → 输出txt文件
- 输入docx文件 → 输出docx文件
- 保持原始文件格式不变

### 2. **文本文件(.txt)**
- 无论输入什么格式，都输出为txt文件
- 使用UTF-8编码保存
- 适合需要纯文本格式的场景

### 3. **Word文档(.docx)**
- 无论输入什么格式，都输出为docx文件
- 保持段落格式
- 适合需要Word文档格式的场景

## 💾 配置保存

输出格式选择会自动保存到配置文件中：

```json
{
  "input_path": "C:/Users/<USER>/Desktop/12.txt",
  "input_mode": "file",
  "output_folder": "C:/Users/<USER>/Desktop",
  "output_format": "txt",
  "api_configs": {
    "primary": {
      "base_url": "https://api.deepseek.com/v1",
      "api_key": "***********************************",
      "model": "deepseek-chat"
    }
  }
}
```

## 📁 文件命名规则

生成的文件名格式：`改写_原文件名.扩展名`

### 示例

| 输入文件 | 输出格式选择 | 输出文件名 |
|---------|-------------|-----------|
| document.txt | 自动 | 改写_document.txt |
| document.txt | Word文档 | 改写_document.docx |
| report.docx | 文本文件 | 改写_report.txt |
| report.docx | 自动 | 改写_report.docx |

### 重名处理

如果文件已存在，会自动添加数字后缀：
- `改写_document.txt`
- `改写_document_1.txt`
- `改写_document_2.txt`

## 🎨 使用场景

### 场景1：保持原格式
- **选择**：自动（保持原格式）
- **适用**：大多数情况，保持文件格式一致性

### 场景2：统一输出为文本
- **选择**：文本文件(.txt)
- **适用**：
  - 需要纯文本格式
  - 批量处理不同格式文件
  - 后续需要文本处理

### 场景3：统一输出为Word文档
- **选择**：Word文档(.docx)
- **适用**：
  - 需要Word格式进行编辑
  - 保持段落格式
  - 便于打印和分享

## ⚙️ 技术实现

### 1. **GUI组件**
```python
# 输出格式选择
self.output_format = tk.StringVar(value="auto")
ttk.Radiobutton(format_frame, text="自动（保持原格式）", variable=self.output_format, value="auto")
ttk.Radiobutton(format_frame, text="文本文件(.txt)", variable=self.output_format, value="txt")
ttk.Radiobutton(format_frame, text="Word文档(.docx)", variable=self.output_format, value="docx")
```

### 2. **文件名生成**
```python
# 确定输出文件扩展名
if format_choice == 'txt':
    output_ext = '.txt'
elif format_choice == 'docx':
    output_ext = '.docx'
else:  # auto - 保持原格式
    output_ext = ext
```

### 3. **文件保存**
```python
# 根据文件扩展名决定保存格式
if ext.lower() == '.txt':
    # 保存为txt文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(text)
else:
    # 保存为docx文件
    doc = Document()
    for para in paragraphs:
        if para.strip():
            doc.add_paragraph(para.strip())
    doc.save(file_path)
```

## 🧪 测试验证

所有功能都经过完整测试：
- ✅ GUI组件正常工作
- ✅ 配置保存和加载
- ✅ 文件名正确生成
- ✅ 文件格式正确保存
- ✅ 重名文件处理

## 🚀 使用步骤

1. **选择输入文件或文件夹**
2. **选择输出文件夹**
3. **选择输出格式**：
   - 自动（推荐）
   - 文本文件
   - Word文档
4. **点击"开始处理"**
5. **查看输出结果**

现在你可以根据需要选择合适的输出格式了！🎉
