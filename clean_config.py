#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理配置文件中的重复API密钥
"""

import json
import os

def clean_config():
    """清理配置文件"""
    if not os.path.exists('config.json'):
        print("❌ 没有找到config.json文件")
        return
    
    try:
        # 读取现有配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("🔍 检查配置文件...")
        
        # 检查重复的API密钥
        api_key_top = config.get('api_key', '')
        api_key_nested = ''
        if 'api_configs' in config and 'primary' in config['api_configs']:
            api_key_nested = config['api_configs']['primary'].get('api_key', '')
        
        if api_key_top and api_key_nested:
            print("⚠️  发现重复的API密钥配置")
            print(f"   顶层api_key: {api_key_top[:10]}...")
            print(f"   嵌套api_key: {api_key_nested[:10]}...")
            
            if api_key_top == api_key_nested:
                print("✅ API密钥一致，移除重复配置")
            else:
                print("⚠️  API密钥不一致，使用嵌套配置中的密钥")
        
        # 使用嵌套配置中的API密钥作为主要密钥
        main_api_key = api_key_nested or api_key_top
        
        if not main_api_key:
            print("⚠️  没有找到API密钥")
        else:
            print(f"✅ 使用API密钥: {main_api_key[:10]}...")
        
        # 创建清理后的配置（移除顶层的api_key字段）
        clean_config = {
            'input_path': config.get('input_path', ''),
            'input_mode': config.get('input_mode', 'folder'),
            'output_folder': config.get('output_folder', ''),
            'api_configs': {
                'primary': {
                    'base_url': "https://api.deepseek.com/v1",
                    'api_key': main_api_key,
                    'model': "deepseek-chat"
                }
            },
            'rewrite_strategies': config.get('rewrite_strategies', {
                'synonym_replacement': True,
                'sentence_restructure': True,
                'paragraph_reorder': True,
                'style_variation': True,
                'back_translation': False
            }),
            'rewrite_ratio': config.get('rewrite_ratio', {
                'min': 70,
                'max': 90
            }),
            'processing_config': config.get('processing_config', {
                'max_text_length': 12000,
                'max_retries': 3,
                'enable_multi_round': True,
                'enable_quality_check': True
            })
        }
        
        # 备份原配置
        with open('config_backup.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print("💾 原配置已备份为 config_backup.json")
        
        # 保存清理后的配置
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(clean_config, f, ensure_ascii=False, indent=2)
        
        print("✅ 配置文件已清理")
        print("\n清理内容:")
        print("• 移除重复的顶层api_key字段")
        print("• 统一使用api_configs.primary.api_key")
        print("• 保留所有用户设置")
        print("• 确保配置结构正确")
        
        # 显示清理后的配置摘要
        print(f"\n📋 配置摘要:")
        print(f"API密钥: {clean_config['api_configs']['primary']['api_key'][:10] if clean_config['api_configs']['primary']['api_key'] else '未设置'}...")
        print(f"输入路径: {clean_config['input_path'] or '未设置'}")
        print(f"输入模式: {clean_config['input_mode']}")
        print(f"输出文件夹: {clean_config['output_folder'] or '未设置'}")
        print(f"多轮改写: {'启用' if clean_config['processing_config']['enable_multi_round'] else '禁用'}")
        print(f"质量检查: {'启用' if clean_config['processing_config']['enable_quality_check'] else '禁用'}")
        
    except Exception as e:
        print(f"❌ 清理配置失败: {e}")
        import traceback
        traceback.print_exc()

def validate_config():
    """验证配置文件"""
    if not os.path.exists('config.json'):
        print("❌ 没有找到config.json文件")
        return False
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("🔍 验证配置文件...")
        
        # 检查是否还有顶层api_key
        if 'api_key' in config:
            print("❌ 仍然存在顶层api_key字段")
            return False
        
        # 检查必要字段
        required_fields = ['input_path', 'input_mode', 'output_folder', 'api_configs']
        missing_fields = []
        
        for field in required_fields:
            if field not in config:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少必要字段: {', '.join(missing_fields)}")
            return False
        
        # 检查API配置
        if 'primary' not in config['api_configs']:
            print("❌ 缺少primary API配置")
            return False
        
        primary_config = config['api_configs']['primary']
        api_required = ['base_url', 'api_key', 'model']
        
        for field in api_required:
            if field not in primary_config:
                print(f"❌ API配置缺少字段: {field}")
                return False
        
        print("✅ 配置文件验证通过")
        print("✅ 没有重复的API密钥配置")
        return True
        
    except Exception as e:
        print(f"❌ 验证配置失败: {e}")
        return False

def main():
    """主函数"""
    print("配置文件清理工具")
    print("=" * 30)
    
    # 先验证当前配置
    if validate_config():
        print("\n当前配置文件正常，无需清理")
        return
    
    print("\n发现配置问题，开始清理...")
    clean_config()
    
    print("\n重新验证配置...")
    if validate_config():
        print("\n🎉 配置清理成功！")
        print("\n现在配置结构:")
        print("• 只有一个API密钥配置位置")
        print("• 位于 api_configs.primary.api_key")
        print("• 没有重复的配置字段")
    else:
        print("\n❌ 配置清理失败，请手动检查")

if __name__ == "__main__":
    main()
