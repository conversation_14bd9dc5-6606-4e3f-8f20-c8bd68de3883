#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多轮改写策略修复
"""

def test_strategy_selection():
    """测试策略选择逻辑"""
    print("测试策略选择逻辑...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 检查改写策略配置
        if hasattr(rewriter, 'rewrite_strategies'):
            strategies = rewriter.rewrite_strategies
            print("✅ 改写策略配置存在")
            
            strategy_names = {
                'synonym_replacement': '同义词替换',
                'sentence_restructure': '句式重构', 
                'paragraph_reorder': '段落重组',
                'style_variation': '风格变换',
                'back_translation': '翻译回译'
            }
            
            print("当前策略配置:")
            for key, name in strategy_names.items():
                status = "✅ 启用" if strategies.get(key, False) else "❌ 禁用"
                print(f"  {name}: {status}")
            
            # 模拟策略选择逻辑
            enabled_strategies = []
            if strategies.get('synonym_replacement', False):
                enabled_strategies.append(('同义词替换', 'synonym_rewrite'))
            if strategies.get('sentence_restructure', False):
                enabled_strategies.append(('句式重构', 'round1_creative'))
            if strategies.get('paragraph_reorder', False):
                enabled_strategies.append(('段落重组', 'round2_polish'))
            if strategies.get('style_variation', False):
                enabled_strategies.append(('风格变换', 'round1_creative'))
            if strategies.get('back_translation', False):
                enabled_strategies.append(('翻译回译', 'back_translation'))
            
            if enabled_strategies:
                print(f"\n✅ 将执行的策略: {', '.join([name for name, _ in enabled_strategies])}")
                return True
            else:
                print("\n⚠️  没有启用任何策略，将使用默认策略")
                return True
                
        else:
            print("❌ 改写策略配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ 策略选择测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompts():
    """测试提示词完整性"""
    print("\n测试提示词完整性...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 检查多轮提示词
        if hasattr(rewriter, 'multi_round_prompts'):
            prompts = rewriter.multi_round_prompts
            print("✅ 多轮提示词存在")
            
            required_prompts = [
                'round1_creative', 'round2_polish', 'synonym_rewrite', 'back_translation'
            ]
            
            missing_prompts = []
            for prompt_name in required_prompts:
                if prompt_name in prompts:
                    print(f"✅ {prompt_name}: 已定义")
                else:
                    missing_prompts.append(prompt_name)
                    print(f"❌ {prompt_name}: 缺失")
            
            if not missing_prompts:
                print("✅ 所有必要的提示词都已定义")
                return True
            else:
                print(f"❌ 缺少提示词: {', '.join(missing_prompts)}")
                return False
        else:
            print("❌ 多轮提示词不存在")
            return False
            
    except Exception as e:
        print(f"❌ 提示词测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_format():
    """测试文件格式处理"""
    print("\n测试文件格式处理...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 测试get_unique_filename方法
        test_paths = [
            "C:/test/document.txt",
            "C:/test/document.docx"
        ]
        
        for test_path in test_paths:
            # 模拟配置
            rewriter.folder_config = {
                'output_folder': 'C:/test/output'
            }
            
            result_path = rewriter.get_unique_filename(test_path)
            
            if result_path:
                import os
                _, ext = os.path.splitext(result_path)
                original_ext = os.path.splitext(test_path)[1]
                
                if ext == original_ext:
                    print(f"✅ {test_path} -> {os.path.basename(result_path)} (格式保持)")
                else:
                    print(f"❌ {test_path} -> {os.path.basename(result_path)} (格式改变)")
                    return False
            else:
                print(f"❌ {test_path} -> 生成文件名失败")
                return False
        
        print("✅ 文件格式处理正确")
        return True
        
    except Exception as e:
        print(f"❌ 文件格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_threading():
    """测试线程处理"""
    print("\n测试线程处理...")
    
    try:
        from main import APIConfigGUI
        
        # 创建GUI实例
        gui = APIConfigGUI()
        
        # 检查是否有线程相关方法
        thread_methods = ['_run_processing', '_processing_completed', '_processing_error']
        
        for method_name in thread_methods:
            if hasattr(gui, method_name):
                print(f"✅ {method_name}: 已定义")
            else:
                print(f"❌ {method_name}: 缺失")
                gui.root.destroy()
                return False
        
        print("✅ 线程处理方法完整")
        gui.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 线程处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("多轮改写策略修复测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 4
    
    try:
        # 1. 测试策略选择逻辑
        if test_strategy_selection():
            success_count += 1
        
        # 2. 测试提示词完整性
        if test_prompts():
            success_count += 1
        
        # 3. 测试文件格式处理
        if test_file_format():
            success_count += 1
        
        # 4. 测试线程处理
        if test_threading():
            success_count += 1
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有修复都成功！")
        print("\n修复内容:")
        print("• 修复了进程阻塞问题（使用后台线程）")
        print("• 修复了文件格式问题（保持原格式）")
        print("• 修复了多轮改写策略（根据用户勾选执行）")
        print("• 添加了所有必要的提示词")
        print("• 修复了max_tokens限制问题")
        print("\n现在程序应该可以正常工作了！")
    else:
        print("⚠️  部分测试失败，请检查修复")

if __name__ == "__main__":
    main()
