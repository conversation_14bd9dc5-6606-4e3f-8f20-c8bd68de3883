#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件编码检测功能
"""

import os

def create_test_files():
    """创建不同编码的测试文件"""
    test_content = "这是一个测试文档。\n包含中文内容用于测试编码检测功能。\n人工智能技术发展迅速。"
    
    # 创建不同编码的文件
    encodings = {
        'utf-8': 'test_utf8.txt',
        'gbk': 'test_gbk.txt',
        'gb2312': 'test_gb2312.txt',
        'big5': 'test_big5.txt'
    }
    
    created_files = []
    
    for encoding, filename in encodings.items():
        try:
            with open(filename, 'w', encoding=encoding) as f:
                f.write(test_content)
            created_files.append((filename, encoding))
            print(f"✅ 创建 {filename} ({encoding})")
        except Exception as e:
            print(f"❌ 创建 {filename} 失败: {e}")
    
    return created_files

def test_encoding_detection():
    """测试编码检测功能"""
    print("测试编码检测功能...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 创建测试文件
        test_files = create_test_files()
        
        if not test_files:
            print("❌ 没有创建成功的测试文件")
            return False
        
        success_count = 0
        
        for filename, expected_encoding in test_files:
            print(f"\n测试文件: {filename} (期望编码: {expected_encoding})")
            
            # 测试编码检测
            detected_encoding = rewriter.detect_encoding(filename)
            print(f"检测到编码: {detected_encoding}")
            
            # 测试文件读取
            content = rewriter.read_file(filename)
            
            if content:
                print(f"✅ 成功读取文件，内容长度: {len(content)} 字符")
                print(f"内容预览: {content[:50]}...")
                success_count += 1
            else:
                print("❌ 文件读取失败")
        
        print(f"\n测试结果: {success_count}/{len(test_files)} 个文件读取成功")
        
        return success_count == len(test_files)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_problematic_file():
    """测试有问题的文件"""
    print("\n测试有问题的文件...")
    
    # 创建一个有编码问题的文件
    problematic_content = b'\xba\xc3\xc4\xe3\xca\xc0\xbd\xe7'  # "你好世界" 的GBK编码
    
    with open('test_problematic.txt', 'wb') as f:
        f.write(problematic_content)
    
    print("✅ 创建有编码问题的测试文件")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 测试编码检测
        detected_encoding = rewriter.detect_encoding('test_problematic.txt')
        print(f"检测到编码: {detected_encoding}")
        
        # 测试文件读取
        content = rewriter.read_file('test_problematic.txt')
        
        if content:
            print(f"✅ 成功读取问题文件，内容: {content}")
            return True
        else:
            print("❌ 问题文件读取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_utf8.txt', 'test_gbk.txt', 'test_gb2312.txt', 
        'test_big5.txt', 'test_problematic.txt'
    ]
    
    for filename in test_files:
        if os.path.exists(filename):
            os.remove(filename)
            print(f"🗑️  已删除 {filename}")

def main():
    """主测试函数"""
    print("文件编码检测功能测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 2
    
    try:
        # 测试编码检测
        if test_encoding_detection():
            success_count += 1
        
        # 测试有问题的文件
        if test_problematic_file():
            success_count += 1
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    finally:
        print("\n清理测试文件...")
        cleanup_test_files()
    
    print("\n" + "=" * 40)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 编码检测功能正常！")
        print("\n功能特性:")
        print("• 自动检测文件编码")
        print("• 支持多种中文编码")
        print("• 智能备用方案")
        print("• 错误容忍处理")
    else:
        print("⚠️  部分测试失败")
        print("\n建议:")
        print("• 确保安装了chardet库: pip install chardet")
        print("• 检查文件权限")

if __name__ == "__main__":
    main()
