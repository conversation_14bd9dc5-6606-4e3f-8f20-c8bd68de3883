#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DeepSeek模型配置修复
"""

def test_model_config():
    """测试模型配置"""
    print("测试模型配置...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 检查MODEL_CONFIG
        if hasattr(rewriter, 'MODEL_CONFIG'):
            config = rewriter.MODEL_CONFIG
            print("✅ MODEL_CONFIG存在")
            
            # 检查必要字段
            required_fields = ['temperature', 'max_tokens', 'max_length']
            for field in required_fields:
                if field in config:
                    print(f"✅ {field}: {config[field]}")
                else:
                    print(f"❌ 缺少字段: {field}")
                    return False
            
            # 检查温度配置
            temp_config = config.get('temperature', {})
            temp_fields = ['creative', 'balanced', 'conservative']
            for field in temp_fields:
                if field in temp_config:
                    print(f"✅ temperature.{field}: {temp_config[field]}")
                else:
                    print(f"❌ 缺少温度配置: {field}")
                    return False
            
            return True
        else:
            print("❌ MODEL_CONFIG不存在")
            return False
            
    except Exception as e:
        print(f"❌ 模型配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_config():
    """测试API配置"""
    print("\n测试API配置...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 检查API配置
        if hasattr(rewriter, 'api_configs') and 'primary' in rewriter.api_configs:
            primary_config = rewriter.api_configs['primary']
            print("✅ API配置存在")
            
            # 检查必要字段
            required_fields = ['base_url', 'api_key', 'model']
            for field in required_fields:
                if field in primary_config:
                    if field == 'api_key':
                        print(f"✅ {field}: {primary_config[field][:10]}...")
                    else:
                        print(f"✅ {field}: {primary_config[field]}")
                else:
                    print(f"❌ 缺少API配置字段: {field}")
                    return False
            
            # 检查模型名称
            model = primary_config.get('model', '')
            if 'deepseek' in model.lower():
                print(f"✅ 使用DeepSeek模型: {model}")
            else:
                print(f"⚠️  模型名称: {model}")
            
            return True
        else:
            print("❌ API配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompts():
    """测试提示词配置"""
    print("\n测试提示词配置...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 检查多轮提示词
        if hasattr(rewriter, 'multi_round_prompts'):
            prompts = rewriter.multi_round_prompts
            print("✅ 多轮提示词存在")
            
            required_prompts = ['round1_creative', 'round2_polish', 'synonym_rewrite']
            for prompt_name in required_prompts:
                if prompt_name in prompts:
                    print(f"✅ {prompt_name}: 已定义")
                else:
                    print(f"❌ 缺少提示词: {prompt_name}")
                    return False
        else:
            print("❌ 多轮提示词不存在")
            return False
        
        # 检查其他提示词
        prompt_attrs = ['general_prompt', 'localization_prompt', 'chinese_to_english_prompt']
        for attr in prompt_attrs:
            if hasattr(rewriter, attr):
                print(f"✅ {attr}: 已定义")
            else:
                print(f"❌ 缺少提示词: {attr}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_process_segment():
    """测试文本处理方法"""
    print("\n测试文本处理方法...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 测试split_text_by_length方法
        test_text = "这是一个测试文本。" * 100  # 创建一个较长的文本
        
        try:
            segments = rewriter.split_text_by_length(test_text)
            print(f"✅ 文本分段成功，分为 {len(segments)} 段")
            
            if segments:
                print(f"   第一段长度: {len(segments[0])} 字符")
                return True
            else:
                print("❌ 分段结果为空")
                return False
                
        except Exception as e:
            print(f"❌ 文本分段失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 文本处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("DeepSeek模型配置修复测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 4
    
    try:
        # 1. 测试模型配置
        if test_model_config():
            success_count += 1
        
        # 2. 测试API配置
        if test_api_config():
            success_count += 1
        
        # 3. 测试提示词配置
        if test_prompts():
            success_count += 1
        
        # 4. 测试文本处理方法
        if test_process_segment():
            success_count += 1
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有配置修复成功！")
        print("\n修复内容:")
        print("• 移除了gpt4o_model引用")
        print("• 统一使用DeepSeek模型配置")
        print("• 修复了MODEL_CONFIG结构")
        print("• 更新了提示词变量名")
        print("\n现在程序应该可以正常使用DeepSeek API了！")
    else:
        print("⚠️  部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
