#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件读取功能
"""

import os

def test_config_loading():
    """测试配置加载"""
    print("测试配置加载...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 检查API配置
        if rewriter.api_configs and 'primary' in rewriter.api_configs:
            api_key = rewriter.api_configs['primary'].get('api_key', '')
            if api_key:
                print(f"✅ API配置加载成功: {api_key[:10]}...")
            else:
                print("❌ API密钥为空")
                return False
        else:
            print("❌ API配置加载失败")
            return False
        
        # 检查文件夹配置
        if rewriter.folder_config:
            input_path = rewriter.folder_config.get('input_path', '')
            output_folder = rewriter.folder_config.get('output_folder', '')
            input_mode = rewriter.folder_config.get('input_mode', '')
            
            print(f"✅ 文件夹配置加载成功")
            print(f"   输入路径: {input_path}")
            print(f"   输入模式: {input_mode}")
            print(f"   输出文件夹: {output_folder}")
            
            if input_path and output_folder:
                return True
            else:
                print("⚠️  路径配置不完整")
                return False
        else:
            print("❌ 文件夹配置加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_reading():
    """测试文件读取"""
    print("\n测试文件读取...")
    
    try:
        from main import TextRewriter
        
        rewriter = TextRewriter()
        
        # 获取配置中的文件路径
        input_path = rewriter.folder_config.get('input_path', '')
        
        if not input_path:
            print("❌ 没有配置输入路径")
            return False
        
        if not os.path.exists(input_path):
            print(f"❌ 文件不存在: {input_path}")
            return False
        
        print(f"测试读取文件: {input_path}")
        
        # 测试编码检测
        encoding = rewriter.detect_encoding(input_path)
        print(f"检测到编码: {encoding}")
        
        # 测试文件读取
        content = rewriter.read_file(input_path)
        
        if content:
            print(f"✅ 文件读取成功")
            print(f"   文件大小: {len(content)} 字符")
            print(f"   内容预览: {content[:100]}...")
            return True
        else:
            print("❌ 文件读取失败")
            return False
            
    except Exception as e:
        print(f"❌ 文件读取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_config_loading():
    """测试GUI配置加载"""
    print("\n测试GUI配置加载...")
    
    try:
        from main import APIConfigGUI
        
        gui = APIConfigGUI()
        
        # 检查API密钥是否正确加载
        api_key = gui.api_key.get()
        if api_key:
            print(f"✅ GUI API密钥加载成功: {api_key[:10]}...")
        else:
            print("❌ GUI API密钥加载失败")
            gui.root.destroy()
            return False
        
        # 检查路径配置
        input_path = gui.input_path.get()
        output_folder = gui.output_folder.get()
        input_mode = gui.input_mode.get()
        
        print(f"✅ GUI路径配置加载成功")
        print(f"   输入路径: {input_path}")
        print(f"   输入模式: {input_mode}")
        print(f"   输出文件夹: {output_folder}")
        
        gui.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI配置加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("文件读取功能测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 3
    
    try:
        # 1. 测试配置加载
        if test_config_loading():
            success_count += 1
        
        # 2. 测试文件读取
        if test_file_reading():
            success_count += 1
        
        # 3. 测试GUI配置加载
        if test_gui_config_loading():
            success_count += 1
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有功能正常！")
        print("\n修复完成:")
        print("• 移除了重复的API密钥配置")
        print("• 添加了自动编码检测功能")
        print("• 配置加载逻辑已优化")
        print("• 支持多种中文编码")
        print("\n现在可以正常处理你的文件了！")
    else:
        print("⚠️  部分测试失败")

if __name__ == "__main__":
    main()
