#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Token显示修复
"""

def test_thread_safe_calls():
    """测试线程安全调用"""
    print("检查线程安全调用...")
    
    try:
        # 读取main.py文件，检查GUI更新调用
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有不安全的GUI调用
        unsafe_patterns = [
            'self.gui.add_log(',
            'self.gui.update_status_display('
        ]
        
        safe_patterns = [
            'self.gui.root.after(0, lambda',
            'self.gui.root.after(0, lambda:'
        ]
        
        lines = content.split('\n')
        unsafe_calls = []
        safe_calls = 0
        
        for i, line in enumerate(lines, 1):
            # 跳过注释行
            if line.strip().startswith('#'):
                continue
            
            # 检查不安全的调用
            for pattern in unsafe_patterns:
                if pattern in line and 'self.gui.root.after' not in line:
                    # 排除在GUI类内部的直接调用
                    if 'def update_status_display' not in lines[max(0, i-5):i]:
                        unsafe_calls.append((i, line.strip()))
            
            # 统计安全调用
            for pattern in safe_patterns:
                if pattern in line:
                    safe_calls += 1
        
        print(f"✅ 找到 {safe_calls} 个线程安全的GUI调用")
        
        if unsafe_calls:
            print(f"⚠️  发现 {len(unsafe_calls)} 个可能不安全的GUI调用:")
            for line_num, line_content in unsafe_calls[:5]:  # 只显示前5个
                print(f"   行 {line_num}: {line_content}")
            return False
        else:
            print("✅ 所有GUI调用都是线程安全的")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_gui_update_logic():
    """测试GUI更新逻辑"""
    print("\n测试GUI更新逻辑...")
    
    try:
        from main import APIConfigGUI
        
        gui = APIConfigGUI()
        
        # 模拟实际的API调用结果
        test_cases = [
            {'input_tokens': 325, 'output_tokens': 252, 'cost': 0.0024},
            {'input_tokens': 400, 'output_tokens': 300, 'cost': 0.0030},
            {'input_tokens': 350, 'output_tokens': 280, 'cost': 0.0026}
        ]
        
        total_input = 0
        total_output = 0
        total_cost = 0.0
        
        for i, case in enumerate(test_cases, 1):
            print(f"模拟第{i}次API调用...")
            
            # 累加期望值
            total_input += case['input_tokens']
            total_output += case['output_tokens']
            total_cost += case['cost']
            
            # 更新GUI
            gui.update_status_display(
                input_tokens=case['input_tokens'],
                output_tokens=case['output_tokens'],
                cost=case['cost']
            )
            
            # 检查累加结果
            expected_total = total_input + total_output
            if gui.total_tokens == expected_total:
                print(f"  ✅ Token累加正确: {expected_total}")
            else:
                print(f"  ❌ Token累加错误: 期望{expected_total}, 实际{gui.total_tokens}")
                gui.root.destroy()
                return False
            
            if abs(gui.total_cost - total_cost) < 0.0001:
                print(f"  ✅ 费用累加正确: {total_cost:.4f}")
            else:
                print(f"  ❌ 费用累加错误: 期望{total_cost:.4f}, 实际{gui.total_cost:.4f}")
                gui.root.destroy()
                return False
        
        # 检查最终显示
        final_token_text = gui.token_info.get()
        final_cost_text = gui.cost_info.get()
        
        print(f"\n最终Token显示: {final_token_text}")
        print(f"最终费用显示: {final_cost_text}")
        
        # 验证显示内容
        if f"总Token: {gui.total_tokens:,}" in final_token_text:
            print("✅ Token显示格式正确")
        else:
            print("❌ Token显示格式错误")
            gui.root.destroy()
            return False
        
        if f"总费用: ¥{gui.total_cost:.4f}" in final_cost_text:
            print("✅ 费用显示格式正确")
        else:
            print("❌ 费用显示格式错误")
            gui.root.destroy()
            return False
        
        gui.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI更新逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_debug_output():
    """测试调试输出"""
    print("\n测试调试输出...")
    
    try:
        from main import APIConfigGUI
        import io
        import sys
        
        # 捕获标准输出
        old_stdout = sys.stdout
        sys.stdout = captured_output = io.StringIO()
        
        gui = APIConfigGUI()
        
        # 触发带调试信息的更新
        gui.update_status_display(input_tokens=100, output_tokens=200, cost=0.001)
        
        # 恢复标准输出
        sys.stdout = old_stdout
        output = captured_output.getvalue()
        
        print(f"捕获的调试输出: {output}")
        
        # 检查是否有调试信息
        if "GUI更新:" in output:
            print("✅ 调试信息输出正常")
            result = True
        else:
            print("❌ 没有调试信息输出")
            result = False
        
        gui.root.destroy()
        return result
        
    except Exception as e:
        print(f"❌ 调试输出测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Token显示修复验证")
    print("=" * 30)
    
    success_count = 0
    total_tests = 3
    
    try:
        # 1. 检查线程安全调用
        if test_thread_safe_calls():
            success_count += 1
        
        # 2. 测试GUI更新逻辑
        if test_gui_update_logic():
            success_count += 1
        
        # 3. 测试调试输出
        if test_debug_output():
            success_count += 1
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    print("\n" + "=" * 30)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 Token显示修复成功！")
        print("\n修复内容:")
        print("• 所有GUI更新调用都使用线程安全方式")
        print("• 添加了详细的调试信息")
        print("• Token和费用累加逻辑正确")
        print("• 显示格式正确")
        print("\n现在重新运行程序，应该能看到正确的Token和费用显示了！")
    else:
        print("⚠️  部分测试失败")

if __name__ == "__main__":
    main()
