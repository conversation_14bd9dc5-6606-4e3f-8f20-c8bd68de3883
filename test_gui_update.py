#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI状态更新功能
"""

import tkinter as tk
import time

def test_gui_status_update():
    """测试GUI状态更新"""
    print("测试GUI状态更新...")
    
    try:
        from main import APIConfigGUI
        
        gui = APIConfigGUI()
        
        # 检查初始状态
        print(f"初始total_tokens: {gui.total_tokens}")
        print(f"初始total_cost: {gui.total_cost}")
        print(f"初始token_info: {gui.token_info.get()}")
        print(f"初始cost_info: {gui.cost_info.get()}")
        
        # 模拟状态更新
        print("\n模拟第一次API调用...")
        gui.update_status_display(input_tokens=325, output_tokens=252, cost=0.0024)
        
        print(f"更新后total_tokens: {gui.total_tokens}")
        print(f"更新后total_cost: {gui.total_cost}")
        print(f"更新后token_info: {gui.token_info.get()}")
        print(f"更新后cost_info: {gui.cost_info.get()}")
        
        # 模拟第二次API调用
        print("\n模拟第二次API调用...")
        gui.update_status_display(input_tokens=400, output_tokens=300, cost=0.0030)
        
        print(f"再次更新后total_tokens: {gui.total_tokens}")
        print(f"再次更新后total_cost: {gui.total_cost}")
        print(f"再次更新后token_info: {gui.token_info.get()}")
        print(f"再次更新后cost_info: {gui.cost_info.get()}")
        
        # 检查累加是否正确
        expected_tokens = 325 + 252 + 400 + 300
        expected_cost = 0.0024 + 0.0030
        
        if gui.total_tokens == expected_tokens:
            print(f"✅ Token累加正确: {expected_tokens}")
        else:
            print(f"❌ Token累加错误: 期望{expected_tokens}, 实际{gui.total_tokens}")
            gui.root.destroy()
            return False
        
        if abs(gui.total_cost - expected_cost) < 0.0001:
            print(f"✅ 费用累加正确: {expected_cost:.4f}")
        else:
            print(f"❌ 费用累加错误: 期望{expected_cost:.4f}, 实际{gui.total_cost:.4f}")
            gui.root.destroy()
            return False
        
        gui.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI状态更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_thread_safe_update():
    """测试线程安全的GUI更新"""
    print("\n测试线程安全的GUI更新...")
    
    try:
        from main import APIConfigGUI
        import threading
        
        gui = APIConfigGUI()
        
        # 模拟在后台线程中更新GUI
        def background_update():
            print("后台线程开始更新...")
            # 使用after方法确保线程安全
            gui.root.after(0, lambda: gui.update_status_display(input_tokens=500, output_tokens=400, cost=0.0040))
            gui.root.after(100, lambda: gui.add_log("后台线程更新完成"))
        
        # 启动后台线程
        thread = threading.Thread(target=background_update, daemon=True)
        thread.start()
        
        # 等待一段时间让更新完成
        gui.root.after(500, lambda: gui.root.quit())
        gui.root.mainloop()
        
        # 检查更新结果
        if gui.total_tokens == 900:  # 500 + 400
            print("✅ 线程安全更新成功")
            result = True
        else:
            print(f"❌ 线程安全更新失败: {gui.total_tokens}")
            result = False
        
        gui.root.destroy()
        return result
        
    except Exception as e:
        print(f"❌ 线程安全更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reset_and_update():
    """测试重置和更新"""
    print("\n测试重置和更新...")
    
    try:
        from main import APIConfigGUI
        
        gui = APIConfigGUI()
        
        # 先更新一些数据
        gui.update_status_display(input_tokens=100, output_tokens=200, cost=0.0010)
        print(f"初始更新后: tokens={gui.total_tokens}, cost={gui.total_cost}")
        
        # 模拟开始处理时的重置
        gui.total_tokens = 0
        gui.total_cost = 0.0
        print(f"重置后: tokens={gui.total_tokens}, cost={gui.total_cost}")
        
        # 再次更新
        gui.update_status_display(input_tokens=325, output_tokens=252, cost=0.0024)
        print(f"重置后更新: tokens={gui.total_tokens}, cost={gui.total_cost}")
        
        if gui.total_tokens == 577 and abs(gui.total_cost - 0.0024) < 0.0001:
            print("✅ 重置和更新正常")
            result = True
        else:
            print("❌ 重置和更新异常")
            result = False
        
        gui.root.destroy()
        return result
        
    except Exception as e:
        print(f"❌ 重置和更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_update():
    """测试日志更新"""
    print("\n测试日志更新...")
    
    try:
        from main import APIConfigGUI
        
        gui = APIConfigGUI()
        
        # 测试添加日志
        gui.add_log("测试日志1")
        gui.add_log("测试日志2")
        gui.add_log("API调用完成 - Token: 577, 费用: ¥0.0024")
        
        # 获取日志内容
        log_content = gui.log_text.get(1.0, tk.END)
        print(f"日志内容: {log_content}")
        
        if "API调用完成" in log_content and "Token: 577" in log_content:
            print("✅ 日志更新正常")
            result = True
        else:
            print("❌ 日志更新异常")
            result = False
        
        gui.root.destroy()
        return result
        
    except Exception as e:
        print(f"❌ 日志更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("GUI状态更新功能测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 4
    
    try:
        # 1. 测试GUI状态更新
        if test_gui_status_update():
            success_count += 1
        
        # 2. 测试线程安全更新
        if test_thread_safe_update():
            success_count += 1
        
        # 3. 测试重置和更新
        if test_reset_and_update():
            success_count += 1
        
        # 4. 测试日志更新
        if test_log_update():
            success_count += 1
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 GUI状态更新功能正常！")
        print("\n修复内容:")
        print("• 添加了线程安全的GUI更新")
        print("• 增加了调试信息输出")
        print("• 修复了状态累加逻辑")
        print("• 确保Token和费用正确显示")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        print("\n可能的问题:")
        print("• 线程安全问题")
        print("• 变量初始化问题")
        print("• GUI组件更新问题")

if __name__ == "__main__":
    main()
